// utils/apiClient.ts
import { StyleBean } from '@/models/Style';
import axios from 'axios';

const styleApiClient = axios.create({
  baseURL: "http://localhost:8009",
  withCredentials: false,
});

styleApiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // window.location.href = '/login';
      console.log("API Respond with 401")
    }
    return Promise.reject(error);
  }
);


export enum StyleEndpoint {
  GET_STYLES = "/get_styles",
  GET_ORIGINAL_STYLES = "/get_original_styles",
  GET_PROCESSED_STYLES = "/get_processed_style",
  GET_MIDI_INFO = "/get_midi_info",
  GET_CONFIG_GENRE = "/config_genres",
  GET_ANNOTATORS = "/get_annotators",
  GET_GENRES = "/get_genres",
  GET_PITCHES = "/get_pitches",
  GET_SCALES = "/get_scales",
  GET_TEMPOS = "/get_tempos",
  GET_SOURCES = "/get_sources",
  GET_SECTIONS = "/get_sections",
  GET_DURATIONS = "/get_durations",

  UPLOAD_STYLE = "/upload_style",
  DELETE_STYLE = "/delete_style",
  TRANSFER_STYLE = "/style_transfer",
  START_REAPER = "/start_reaper",

  TS_RENDER = "/twelve_scale_render"
}




export class StyleAPIService {
  static async getStyles(filter: Record<string, any>, pagination?: { page: number, pageSize: number }): Promise<{ styles: StyleBean[], paginated_cursor: any }>   {
    const params = pagination ? `?page=${pagination.page}&page_size=${pagination.pageSize}` : '';
    return (await styleApiClient.post(`${StyleEndpoint.GET_STYLES}${params}`, filter)).data.data;
  }

  static async getOriginalStyles(filter: Record<string, any>):Promise<{ styles: StyleBean[], paginated_cursor: any }> {
    return (await styleApiClient.post(`${StyleEndpoint.GET_ORIGINAL_STYLES}`, filter)).data.data;
  }

  static async getProcessedStyle(rawStyleId: string): Promise<{ styles: StyleBean[], paginated_cursor: any }> {
    return (await styleApiClient.post(`${StyleEndpoint.GET_PROCESSED_STYLES}/${rawStyleId}`)).data.data;
  }

  static async getMidiInfo(data: { midi_path: string; time_signature: string; tempo: string }) {
    return (await styleApiClient.post(StyleEndpoint.GET_MIDI_INFO, data)).data.data;
  }

  static async getConfigGenres() {
    return (await styleApiClient.get(StyleEndpoint.GET_CONFIG_GENRE)).data.data;
  }

  static async getGenres(filter: Record<string, any>): Promise<string[]> {
    return (await styleApiClient.post(StyleEndpoint.GET_GENRES, filter)).data.data;
  }

  static async getScales(filter: Record<string, any>):Promise<string[]> {
    return (await styleApiClient.post(StyleEndpoint.GET_SCALES, filter)).data.data;
  }

  static async getPitches(filter: Record<string, any>):Promise<string[]> {
    return (await styleApiClient.post(StyleEndpoint.GET_PITCHES, filter)).data.data;
  }

  static async getDurations(filter: Record<string, any>):Promise<string[]> {
    return (await styleApiClient.post(StyleEndpoint.GET_DURATIONS, filter)).data.data;
  }

  static async getSections(filter: Record<string, any>):Promise<string[]> {
    return (await styleApiClient.post(StyleEndpoint.GET_SECTIONS, filter)).data.data;
  }

  static async getSources(filter: Record<string, any>):Promise<string[]> {
    return (await styleApiClient.post(StyleEndpoint.GET_SOURCES, filter)).data.data;
  }

  static async getTempos(filter: Record<string, any>):Promise<string[]> {
    return (await styleApiClient.post(StyleEndpoint.GET_TEMPOS, filter)).data.data;
  }

  static async getAnnotators(filter: Record<string, any>):Promise<string[]> {
    return (await styleApiClient.post(StyleEndpoint.GET_ANNOTATORS, filter)).data.data;
  }

  static async uploadStyle(data: Record<string, any>) {
    return (await styleApiClient.post(StyleEndpoint.UPLOAD_STYLE, data)).data.data;
  }

  static async deleteStyle(id: string) {
    return (await styleApiClient.delete(`${StyleEndpoint.DELETE_STYLE}/${id}`)).data.data;
  }

  static async styleTransfer(data: Record<string, any>) {
    return (await styleApiClient.post(StyleEndpoint.TRANSFER_STYLE, data)).data;
  }

  static async startReaper() {
    return (await styleApiClient.get(StyleEndpoint.START_REAPER)).data;
  }

  static async twelveScaleRender(data: Record<string, any>) {
    return (await styleApiClient.post(StyleEndpoint.TS_RENDER, data)).data;
  }

  static async chatInitialFileUpload(data: Record<string, any>) {
    return (await styleApiClient.post("/chat/file_uploaded", data)).data;
  }

  static async chatAskAI(data: Record<string, any>) {
    return (await styleApiClient.post("/chat/ask", data)).data;
  }

  static async chatReset() {
    return (await styleApiClient.post("/chat/reset")).data;
  }
}

export { styleApiClient };
export default StyleAPIService;
