import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Music,
  Clock,
  User,
  Tag,
  FileMusic,
  Upload as UploadIcon,
  FolderOpen,
  FileArchive,
  AlertCircle,
  FolderOutput,
  RotateCcw,
  CheckCircle2
} from "lucide-react";
import { Button } from "@/components/ui/button";
import AudioPlayer from "@/components/audio-player/AudioPlayer";
import FullScreenLoader from "@/components/screens/FullScreenLoader";
import ConfirmationDialog from "@/components/dialog-box/ConfirmationDialog";
import SuccessDialog from "@/components/dialog-box/SuccessDialog";
import StyleAPIService from "@/utils/styleApiService";
import commons from "@/utils/Commons";
import { TimeSignList, TimeSignKeyMap, SongScaleList, SongScaleKeyMap } from "@animesh-melodyze/ts-shared";
import { AnnotatorList } from "@/models/Style";
import { open } from "@tauri-apps/plugin-dialog";
import { readFile } from "@tauri-apps/plugin-fs";
import { MdOutlineSwipe } from "react-icons/md";

const StyleUploadPage: React.FC = () => {
  // Form state
  const [time_signature, setTimeSignature] = useState<string>("");
  const [swing, setSwing] = useState<boolean>(false);
  const [genre, setGenre] = useState<string>("");
  const [source, setSource] = useState<string>("");
  const [pitch, setPitch] = useState<string>("");
  const [scale, setScale] = useState<string>("");
  const [tempo, setTempo] = useState<string>("");
  const [annotator, setAnnotator] = useState<string>("");

  // File paths
  const [projectFilePath, setProjectFilePath] = useState<string>("");
  const [midiFilePath, setMidiFilePath] = useState<string>("");
  const [audioFilePath, setAudioFilePath] = useState<string>("");
  const [audioWithVocalsFilePath, setAudioWithVocalsFilePath] = useState<string>("");
  const [outputDirectory, setOutputDirectory] = useState<string>("");

  // Audio sources for preview
  const [audioSrc, setAudioSrc] = useState<string>("");
  const [audioWithVocalsSrc, setAudioWithVocalsSrc] = useState<string>("");

  // UI state
  const [isFormFilled, setIsFormFilled] = useState<boolean>(false);
  const [isLoaderOpen, setIsLoaderOpen] = useState<boolean>(false);
  const [loaderProgress, setLoaderProgress] = useState<number>(0);
  const [loaderMessage, setLoaderMessage] = useState<string>("");
  const [showSuccessDialog, setShowSuccessDialog] = useState<boolean>(false);
  const [isConfirmationDialogOpen, setIsConfirmationDialogOpen] = useState<boolean>(false);
  const [confirmationDialogConfig, setConfirmationDialogConfig] = useState({
    title: "",
    message: "",
    onConfirm: async () => { },
  });

  // Dropdown options
  const [genreList, setGenreList] = useState<string[]>([]);

  // Check if all required fields are filled
  const checkFormFillStatus = () => {
    return (
      !!time_signature &&
      !!genre &&
      !!source &&
      !!pitch &&
      !!scale &&
      !!tempo &&
      !!annotator &&
      !!projectFilePath &&
      !!midiFilePath &&
      !!audioFilePath &&
      !!audioWithVocalsFilePath &&
      !!outputDirectory
    );
  };

  // Handle file selection for project file
  const handleProjectSelect = async () => {
    try {
      const filePath = await open({
        directory: false,
        multiple: false,
        filters: [{ name: "ZIP Files", extensions: ["zip"] }],
      });

      if (filePath && typeof filePath === "string") {
        setProjectFilePath(filePath);
      }
    } catch (error) {
      console.error("Error selecting project file:", error);
    }
  };

  // Handle file selection for MIDI file
  const handleMidiSelect = async () => {
    try {
      const filePath = await open({
        directory: false,
        multiple: false,
        filters: [{ name: "ZIP Files", extensions: ["zip"] }],
      });

      if (filePath && typeof filePath === "string") {
        setMidiFilePath(filePath);
      }
    } catch (error) {
      console.error("Error selecting MIDI file:", error);
    }
  };

  // Handle file selection for audio file
  const handleAudioSelect = async () => {
    try {
      const filePath = await open({
        directory: false,
        multiple: false,
        filters: [{ name: "Audio Files", extensions: ["mp3"] }],
      });

      if (filePath && typeof filePath === "string") {
        setAudioFilePath(filePath);
        try {
          const audioData = await readFile(filePath);
          const buffer = new ArrayBuffer(audioData.byteLength);
          const view = new Uint8Array(buffer);
          view.set(audioData);
          // Now create Blob from the clean Uint8Array
          const blob = new Blob([view], { type: "audio/mpeg" });
          const url = URL.createObjectURL(blob);
          setAudioSrc(url);
        } catch (error) {
          console.error("Error reading audio file:", error);
        }
      }
    } catch (error) {
      console.error("Error selecting audio file:", error);
    }
  };

  // Handle file selection for audio with vocals file
  const handleAudioWithVocalsSelect = async () => {
    try {
      const filePath = await open({
        directory: false,
        multiple: false,
        filters: [{ name: "Audio Files", extensions: ["mp3"] }],
      });

      if (filePath && typeof filePath === "string") {
        setAudioWithVocalsFilePath(filePath);
        try {
          const audioData = await readFile(filePath);
          const buffer = new ArrayBuffer(audioData.byteLength);
          const view = new Uint8Array(buffer);
          view.set(audioData);
          // Now create Blob from the clean Uint8Array
          const blob = new Blob([view], { type: "audio/mpeg" });
          const url = URL.createObjectURL(blob);
          setAudioWithVocalsSrc(url);
        } catch (error) {
          console.error("Error reading audio with vocals file:", error);
        }
      }
    } catch (error) {
      console.error("Error selecting audio with vocals file:", error);
    }
  };

  // Handle output directory selection
  const handleOutputDirectoryChange = async () => {
    try {
      const dirPath = await open({
        directory: true,
        multiple: false,
      });

      if (dirPath && typeof dirPath === "string") {
        setOutputDirectory(dirPath);
      }
    } catch (error) {
      console.error("Error selecting output directory:", error);
    }
  };

  // Reset form
  const handleFormReset = () => {
    setTimeSignature("");
    setSwing(false);
    setGenre("");
    setSource("");
    setPitch("");
    setScale("");
    setTempo("");
    setAnnotator("");
    setProjectFilePath("");
    setMidiFilePath("");
    setAudioFilePath("");
    setAudioWithVocalsFilePath("");
    setOutputDirectory("");
    setAudioSrc("");
    setAudioWithVocalsSrc("");
  };

  // Handle form submission
  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setIsLoaderOpen(true);
    setLoaderProgress(10);
    setLoaderMessage("Preparing files...");

    try {
      const payload = {
        time_signature,
        swing,
        genre,
        source,
        pitch,
        scale,
        tempo,
        annotator,
        project_file_name: commons.getFileNameAndExtension(projectFilePath).path,
        project_zip_path: projectFilePath,
        midi_zip_path: midiFilePath,
        audio_path: audioFilePath,
        audio_with_vocals_path: audioWithVocalsFilePath,
        output_directory: outputDirectory,
      };

      console.log("STYLE UPLOAD PAYLOAD:", payload);
      setLoaderMessage("Final Save...");
      await StyleAPIService.uploadStyle(payload);
      setLoaderProgress(100);
      setIsLoaderOpen(false);
      setShowSuccessDialog(true);
    } catch (error: any) {
      console.error(error);
      alert(`Error uploading style: ${error.message || error}`);
      setIsLoaderOpen(false);
    }
  };

  // Handle success dialog close
  const handleSuccessDialogClose = () => {
    handleFormReset();
    setShowSuccessDialog(false);
    commons.hardResfresh();
  };

  // Show form reset confirmation
  const showFormResetConfirmation = () => {
    setConfirmationDialogConfig({
      title: "Reset Form",
      message: "Are you sure you want to reset this form? All entered data will be lost.",
      onConfirm: async () => {
        setIsConfirmationDialogOpen(false);
        handleFormReset();
      },
    });
    setIsConfirmationDialogOpen(true);
  };

  // Show submit confirmation
  const showSubmitConfirmation = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setConfirmationDialogConfig({
      title: "Confirm Submission",
      message: "Are you sure you want to submit this style?",
      onConfirm: async () => {
        setIsConfirmationDialogOpen(false);
        await handleSubmit(event);
      },
    });
    setIsConfirmationDialogOpen(true);
  };

  // Fetch genre list on component mount
  useEffect(() => {
    const fetchGenreList = async () => {
      try {
        const genres: any[] = await StyleAPIService.getConfigGenres();
        setGenreList(genres);
        setIsFormFilled(checkFormFillStatus());
      } catch (error: any) {
        alert(`Error fetching genres: ${error.message}`);
      }
    };
    fetchGenreList();
  }, []);

  // Update form fill status when any field changes
  useEffect(() => {
    setIsFormFilled(checkFormFillStatus());
  }, [
    time_signature,
    genre,
    source,
    pitch,
    scale,
    tempo,
    annotator,
    audioFilePath,
    audioWithVocalsFilePath,
    midiFilePath,
    projectFilePath,
    outputDirectory,
  ]);

  return (
    <motion.div
      className="min-h-screen bg-gradient-to-br from-melodyze-secondary via-melodyze-tertiary to-black text-gray-100 font-inter"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <FullScreenLoader isOpen={isLoaderOpen} message={loaderMessage} progress={loaderProgress} />
      <SuccessDialog
        isOpen={showSuccessDialog}
        message={"Your style has been uploaded successfully."}
        onConfirm={handleSuccessDialogClose}
      />
      <ConfirmationDialog
        isOpen={isConfirmationDialogOpen}
        title={confirmationDialogConfig.title}
        message={confirmationDialogConfig.message}
        onConfirm={confirmationDialogConfig.onConfirm}
        onCancel={() => setIsConfirmationDialogOpen(false)}
      />

      <motion.div
        className="container mx-auto px-6 py-10"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <motion.div
          className="flex justify-between items-center mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <motion.h1
            className="text-4xl font-iceland font-bold flex items-center bg-gradient-to-r from-melodyze-cyan via-melodyze-purple to-melodyze-pink bg-clip-text text-transparent"
            whileHover={{ scale: 1.02 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <motion.div whileHover={{ rotate: 360, scale: 1.1 }} transition={{ duration: 0.5 }}>
              <UploadIcon className="mr-4 text-melodyze-cyan h-8 w-8" />
            </motion.div>
            Upload Style
          </motion.h1>
        </motion.div>

        <motion.div
          className="bg-melodyze-secondary/30 backdrop-blur-xl border-2 border-melodyze-cyan/20 rounded-2xl overflow-hidden shadow-2xl relative"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <AnimatePresence mode="wait">
            {isFormFilled ? (
              <motion.div
                key="success"
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                exit={{ scale: 0, rotate: 180 }}
                transition={{ type: "spring", stiffness: 300 }}
                className="absolute top-6 right-6 z-10"
              >
                <CheckCircle2 className="text-melodyze-cyan w-7 h-7" />
              </motion.div>
            ) : (
              <motion.div
                key="warning"
                initial={{ scale: 0, rotate: 180 }}
                animate={{ scale: 1, rotate: 0 }}
                exit={{ scale: 0, rotate: -180 }}
                transition={{ type: "spring", stiffness: 300 }}
                className="absolute top-6 right-6 z-10 cursor-pointer"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <AlertCircle className="text-melodyze-pink w-7 h-7" />
              </motion.div>
            )}
          </AnimatePresence>
          <form onSubmit={showSubmitConfirmation} className="p-8 space-y-10">
            {/* Basic Information Section */}
            <motion.div
              className="space-y-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.5 }}
            >
              <motion.h2
                className="text-2xl font-iceland font-bold text-white flex items-center bg-gradient-to-r from-melodyze-cyan to-melodyze-purple bg-clip-text text-transparent"
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <motion.div whileHover={{ rotate: 15, scale: 1.1 }} transition={{ duration: 0.3 }}>
                  <Music className="mr-3 text-melodyze-cyan h-6 w-6" />
                </motion.div>
                Basic Information
              </motion.h2>
              <motion.div
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.6, staggerChildren: 0.1 }}
              >
                {/* Annotator */}
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <User className="text-indigo-400 h-4 w-4" />
                    <label htmlFor="annotator" className="text-sm font-medium text-gray-300">
                      Annotator <span className="text-red-500">*</span>
                    </label>
                  </div>
                  <div className="relative">
                    <select
                      id="annotator"
                      className="w-full bg-gray-900/70 border border-gray-700 rounded-md py-2 px-3 text-sm text-gray-200 appearance-none focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-200"
                      value={annotator}
                      onChange={(e) => setAnnotator(e.target.value)}
                      required
                    >
                      <option value="" disabled>
                        Select annotator
                      </option>
                      {AnnotatorList.map((name, i) => (
                        <option key={i} value={name}>
                          {name}
                        </option>
                      ))}
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                      <svg className="h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path
                          fillRule="evenodd"
                          d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                  </div>
                </div>

                {/* Time Signature */}
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Clock className="text-indigo-400 h-4 w-4" />
                    <label htmlFor="timeSignature" className="text-sm font-medium text-gray-300">
                      Time Signature <span className="text-red-500">*</span>
                    </label>
                  </div>
                  <div className="relative">
                    <select
                      id="timeSignature"
                      className="w-full bg-gray-900/70 border border-gray-700 rounded-md py-2 px-3 text-sm text-gray-200 appearance-none focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-200"
                      value={time_signature || ""}
                      onChange={(e) => setTimeSignature(e.target.value)}
                      required
                    >
                      <option value="" disabled>
                        Select time signature
                      </option>
                      {TimeSignList.map((ts, i) => (
                        <option key={i} value={TimeSignKeyMap[ts]}>
                          {ts}
                        </option>
                      ))}
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                      <svg className="h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path
                          fillRule="evenodd"
                          d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                  </div>
                </div>

                {/* Swing */}
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <MdOutlineSwipe className="text-indigo-400 h-4 w-4" />
                    <label htmlFor="swing" className="text-sm font-medium text-gray-300">
                      Swing <span className="text-red-500">*</span>
                    </label>
                  </div>
                  <div className="flex items-center h-[38px]">
                    <button
                      type="button"
                      onClick={() => setSwing(!swing)}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:ring-offset-2 ${swing ? "bg-pink-600/80" : "bg-gray-600"
                        }`}
                    >
                      <span
                        className={`inline-block h-5 w-5 transform rounded-full bg-white transition-transform ${swing ? "translate-x-5" : "translate-x-1"
                          }`}
                      />
                    </button>
                    <span className="ml-3 text-sm text-gray-300">{swing ? "Yes" : "No"}</span>
                  </div>
                </div>
              </motion.div>
            </motion.div>

            {/* Output Directory */}
            <motion.div
              className="space-y-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.7 }}
            >
              <motion.h2
                className="text-2xl font-iceland font-bold text-white flex items-center bg-gradient-to-r from-melodyze-purple to-melodyze-pink bg-clip-text text-transparent"
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <motion.div whileHover={{ rotate: -15, scale: 1.1 }} transition={{ duration: 0.3 }}>
                  <FolderOutput className="mr-3 text-melodyze-purple h-6 w-6" />
                </motion.div>
                Output Location
              </motion.h2>
              <div className="space-y-2">
                <label htmlFor="outputDirectory" className="text-sm font-medium text-gray-300">
                  Output Directory <span className="text-red-500">*</span>
                </label>
                <button
                  type="button"
                  onClick={handleOutputDirectoryChange}
                  className="w-full flex items-center justify-between bg-gray-900/70 border border-gray-700 rounded-md py-2 px-3 text-sm text-gray-200 hover:bg-gray-700/70 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-cyan-500"
                >
                  <span className="flex items-center truncate">
                    <FolderOpen className="mr-2 h-4 w-4 text-indigo-400" />
                    <span className="truncate">{outputDirectory || "Select Output Folder"}</span>
                  </span>
                  <span className="text-gray-400">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                      />
                    </svg>
                  </span>
                </button>
              </div>
            </motion.div>

            {/* Song Details */}
            <div className="space-y-4">
              <h2 className="text-lg font-semibold text-white flex items-center">
                <Tag className="mr-2 text-indigo-400 h-5 w-5" /> Song Details
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {/* Genre */}
                <div className="space-y-2">
                  <label htmlFor="genre" className="text-sm font-medium text-gray-300">
                    Genre <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <select
                      id="genre"
                      className="w-full bg-gray-900/70 border border-gray-700 rounded-md py-2 px-3 text-sm text-gray-200 appearance-none focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-200"
                      value={genre || ""}
                      onChange={(e) => setGenre(e.target.value)}
                      required
                    >
                      <option value="" disabled>
                        Select a genre
                      </option>
                      {genreList.map((val, index) => (
                        <option key={index} value={val}>
                          {val}
                        </option>
                      ))}
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                      <svg className="h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path
                          fillRule="evenodd"
                          d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                  </div>
                </div>

                {/* Key */}
                <div className="space-y-2">
                  <label htmlFor="pitch" className="text-sm font-medium text-gray-300">
                    Key <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <select
                      id="pitch"
                      className="w-full bg-gray-900/70 border border-gray-700 rounded-md py-2 px-3 text-sm text-gray-200 appearance-none focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-200"
                      value={pitch || ""}
                      onChange={(e) => setPitch(e.target.value)}
                      required
                    >
                      <option value="" disabled>
                        Select a key
                      </option>
                      {SongScaleList.map((sc, i) => (
                        <option key={i} value={SongScaleKeyMap[sc]}>
                          {sc}
                        </option>
                      ))}
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                      <svg className="h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path
                          fillRule="evenodd"
                          d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                  </div>
                </div>

                {/* Scale */}
                <div className="space-y-2">
                  <label htmlFor="scale" className="text-sm font-medium text-gray-300">
                    Scale <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <select
                      id="scale"
                      className="w-full bg-gray-900/70 border border-gray-700 rounded-md py-2 px-3 text-sm text-gray-200 appearance-none focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-200"
                      value={scale || ""}
                      onChange={(e) => setScale(e.target.value)}
                      required
                    >
                      <option value="" disabled>
                        Select a scale
                      </option>
                      <option value="major">Major</option>
                      <option value="minor">Minor</option>
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                      <svg className="h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path
                          fillRule="evenodd"
                          d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                  </div>
                </div>

                {/* Tempo */}
                <div className="space-y-2">
                  <label htmlFor="tempo" className="text-sm font-medium text-gray-300">
                    Tempo <span className="text-red-500">*</span>
                  </label>
                  <input
                    id="tempo"
                    type="number"
                    className="w-full bg-gray-900/70 border border-gray-700 rounded-md py-2 px-3 text-sm text-gray-200 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-200"
                    min={60}
                    max={300}
                    value={tempo}
                    onChange={(e) => setTempo(e.target.value)}
                    onWheel={(e) => e.currentTarget.blur()}
                    required
                    placeholder="Enter tempo (60-300)"
                  />
                </div>

                {/* Source */}
                <div className="space-y-2">
                  <label htmlFor="source" className="text-sm font-medium text-gray-300">
                    Source <span className="text-red-500">*</span>
                  </label>
                  <input
                    id="source"
                    type="text"
                    value={source}
                    onChange={(e) => setSource(e.target.value.trim())}
                    className="w-full bg-gray-900/70 border border-gray-700 rounded-md py-2 px-3 text-sm text-gray-200 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-200"
                    placeholder="Enter song name"
                    required
                  />
                </div>
              </div>
            </div>

            {/* Files Section */}
            <motion.div
              className="space-y-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.8 }}
            >
              <motion.h2
                className="text-2xl font-iceland font-bold text-white flex items-center bg-gradient-to-r from-melodyze-pink to-melodyze-indigo bg-clip-text text-transparent"
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <motion.div whileHover={{ rotate: 360, scale: 1.1 }} transition={{ duration: 0.5 }}>
                  <FileArchive className="mr-3 text-melodyze-pink h-6 w-6" />
                </motion.div>
                Project Files
              </motion.h2>
              <motion.div
                className="grid grid-cols-1 md:grid-cols-2 gap-8"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.9, staggerChildren: 0.1 }}
              >
                {/* Project File */}
                <div className="space-y-2">
                  <label htmlFor="projectFile" className="text-sm font-medium text-gray-300">
                    Project file (.zip) <span className="text-red-500">*</span>
                  </label>
                  <button
                    type="button"
                    onClick={handleProjectSelect}
                    className="w-full flex items-center justify-between bg-gray-900/70 border border-gray-700 rounded-md py-2 px-3 text-sm text-gray-200 hover:bg-gray-700/70 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-cyan-500"
                  >
                    <span className="flex items-center truncate">
                      <FileArchive className="mr-2 h-4 w-4 text-indigo-400" />
                      <span className="truncate">{projectFilePath || "Browse a file"}</span>
                    </span>
                    <span className="text-gray-400">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                        />
                      </svg>
                    </span>
                  </button>
                </div>

                {/* MIDI File */}
                <div className="space-y-2">
                  <label htmlFor="midiFile" className="text-sm font-medium text-gray-300">
                    MIDI file (.zip) <span className="text-red-500">*</span>
                  </label>
                  <button
                    type="button"
                    onClick={handleMidiSelect}
                    className="w-full flex items-center justify-between bg-gray-900/70 border border-gray-700 rounded-md py-2 px-3 text-sm text-gray-200 hover:bg-gray-700/70 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-cyan-500"
                  >
                    <span className="flex items-center truncate">
                      <FileArchive className="mr-2 h-4 w-4 text-indigo-400" />
                      <span className="truncate">{midiFilePath || "Browse a file"}</span>
                    </span>
                    <span className="text-gray-400">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                        />
                      </svg>
                    </span>
                  </button>
                </div>
              </motion.div>
            </motion.div>

            {/* Audio Files Section */}
            <motion.div
              className="space-y-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 1.0 }}
            >
              <motion.h2
                className="text-2xl font-iceland font-bold text-white flex items-center bg-gradient-to-r from-melodyze-indigo to-melodyze-cyan bg-clip-text text-transparent"
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <motion.div whileHover={{ scale: 1.2 }} transition={{ duration: 0.3 }}>
                  <FileMusic className="mr-3 text-melodyze-indigo h-6 w-6" />
                </motion.div>
                Audio Files
              </motion.h2>
              <motion.div
                className="grid grid-cols-1 md:grid-cols-2 gap-8"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 1.1, staggerChildren: 0.1 }}
              >
                {/* Karaoke Audio */}
                <div className="space-y-2">
                  <label htmlFor="audioFile" className="text-sm font-medium text-gray-300">
                    Karaoke Audio (.mp3) <span className="text-red-500">*</span>
                  </label>
                  <button
                    type="button"
                    onClick={handleAudioSelect}
                    className="w-full flex items-center justify-between bg-gray-900/70 border border-gray-700 rounded-md py-2 px-3 text-sm text-gray-200 hover:bg-gray-700/70 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-cyan-500"
                  >
                    <span className="flex items-center truncate">
                      <FileMusic className="mr-2 h-4 w-4 text-indigo-400" />
                      <span className="truncate">{audioFilePath || "Browse a file"}</span>
                    </span>
                    <span className="text-gray-400">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                        />
                      </svg>
                    </span>
                  </button>
                  {audioSrc && <AudioPlayer src={audioSrc} label="Karaoke Audio Preview" />}
                </div>

                {/* Audio with Vocals */}
                <div className="space-y-2">
                  <label htmlFor="audioWithVocalsFile" className="text-sm font-medium text-gray-300">
                    Audio with Vocals (.mp3) <span className="text-red-500">*</span>
                  </label>
                  <button
                    type="button"
                    onClick={handleAudioWithVocalsSelect}
                    className="w-full flex items-center justify-between bg-gray-900/70 border border-gray-700 rounded-md py-2 px-3 text-sm text-gray-200 hover:bg-gray-700/70 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-cyan-500"
                  >
                    <span className="flex items-center truncate">
                      <FileMusic className="mr-2 h-4 w-4 text-indigo-400" />
                      <span className="truncate">{audioWithVocalsFilePath || "Browse a file"}</span>
                    </span>
                    <span className="text-gray-400">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                        />
                      </svg>
                    </span>
                  </button>
                  {audioWithVocalsSrc && <AudioPlayer src={audioWithVocalsSrc} label="Audio with Vocals Preview" />}
                </div>
              </motion.div>
            </motion.div>

            {/* Form Actions */}
            <motion.div
              className="flex flex-col sm:flex-row justify-end space-y-4 sm:space-y-0 sm:space-x-6 pt-8 border-t border-melodyze-cyan/20"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 1.2 }}
            >
              <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                <Button
                  type="submit"
                  disabled={!isFormFilled}
                  className="px-10 py-3 flex items-center font-inter font-semibold bg-gradient-to-r from-melodyze-pink via-melodyze-purple to-melodyze-indigo transition-all duration-500 hover:from-melodyze-pink/80 hover:via-melodyze-purple/80 hover:to-melodyze-indigo/80 shadow-lg hover:shadow-melodyze-purple/25 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <UploadIcon className="mr-3 h-5 w-5" />
                  Upload Style
                </Button>
              </motion.div>

              <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                <Button
                  type="button"
                  onClick={showFormResetConfirmation}
                  variant="outline"
                  className="px-8 py-3 font-inter font-medium text-gray-300 border-melodyze-cyan/30 hover:bg-melodyze-cyan/10 hover:border-melodyze-cyan/50 transition-all duration-300"
                >
                  <RotateCcw className="mr-3 h-4 w-4" /> Reset Form
                </Button>
              </motion.div>
            </motion.div>
          </form>
        </motion.div>
      </motion.div>
    </motion.div>
  );
};

export default StyleUploadPage;
