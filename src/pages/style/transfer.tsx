import React, { useState, useEffect, ChangeEvent, FormEvent } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  MusicIcon,
  ClockIcon,
  UserIcon,
  TagIcon,
  FileMusicIcon,
  FolderOpenIcon,
  FolderOutputIcon,
  FileArchive,
  AlertCircle,
  CheckCircle2,
  RefreshCw,
  UploadIcon,
  SettingsIcon,
  Volume2,
  ArrowRight,
  RotateCcw,
  KeyRoundIcon,
  GaugeCircle,
  PlayIcon,
  GuitarIcon,
} from "lucide-react";
import { Button } from "@/components/ui/button";

import commons from "../../utils/Commons";
import StyleAPIService from "../../utils/styleApiService";
import { SongScaleKeyMap, SongScaleList, TimeSignKeyMap, TimeSignList } from "@animesh-melodyze/ts-shared";
import ConfirmationDialog from "../../components/dialog-box/ConfirmationDialog";
import SelectAudioItemDialog, { FetchStyleResult } from "../../components/dialog-box/SelectableAudioPlayer";
import SuccessDialog from "../../components/dialog-box/SuccessDialog";
import PaginatedDropdown from "../../components/PaginatedDropdown";
import FullScreenLoader from "../../components/screens/FullScreenLoader";
import { open } from "@tauri-apps/plugin-dialog";
import { AnnotatorList, SongPitchKeyMap, SongPitchList, StyleBean } from "@/models/Style";
import { VscSymbolRuler } from "react-icons/vsc";
import { MdOutlineSwipe } from "react-icons/md";

interface StyleTransferProps { }

interface StyleSection {
  styleId?: string;
  slNo: number;
  midiSection: string;
  isBlank: boolean;

  startBar: number | "";
  startBeat: number | "";
  endBar: number | "";
  endBeat: number | "";

  genre?: string;
  source?: string;
  scale?: string;
  pitch?: string;
  durationBar?: string;
  tempo?: string;
  sectionName?: string;
  annotator?: string;
}

interface IStyleTransferForm {
  inputSongName: string;
  inputTimeSignature: string;
  inputSwing: boolean;
  inputGenre: string;
  inputPitch: string;
  inputAnnotator: string;
  inputScale: string;
  inputTempo: string;
  inputMidiPath: string;
  outputDir: string;
  sections: StyleSection[];
}

const StyleTransferPage: React.FC<StyleTransferProps> = () => {
  const [form, setForm] = useState<IStyleTransferForm>({
    inputSongName: "",
    inputTimeSignature: "",
    inputSwing: false,
    inputGenre: "",
    inputScale: "",
    inputTempo: "",
    inputAnnotator: "",
    inputPitch: "",
    inputMidiPath: "",
    outputDir: "",
    sections: [],
  });

  const [genreList, setGenreList] = useState<string[]>([]);
  const [isMainInputFormFilled, setIsMainInputFormFilled] = useState<boolean>(false);

  const [isMidiLoading, setIsMidiLoading] = useState<boolean>(false);
  const [isLoaderOpen, setIsLoaderOpen] = useState<boolean>(false);
  const [loaderProgress, setLoaderProgress] = useState<number>(0);
  const [loaderMessage, setLoaderMessage] = useState<string>("");
  const [showSuccessDialog, setShowSuccessDialog] = useState<boolean>(false);
  const [isConfirmationDialogOpen, setIsConfirmationDialogOpen] = useState(false);
  const [confirmationDialogConfig, setConfirmationDialogConfig] = useState({
    title: "",
    message: "",
    onConfirm: () => { },
  });

  const getMainInputFillStatus = (form: IStyleTransferForm) => {
    return (
      !!form.inputSongName &&
      !!form.inputMidiPath &&
      !!form.inputTimeSignature &&
      form.inputSwing !== null &&
      !!form.inputGenre &&
      !!form.inputScale &&
      !!form.inputTempo &&
      !!form.inputAnnotator &&
      !!form.inputPitch &&
      !!form.outputDir &&
      Number(form.inputTempo) >= 60 &&
      Number(form.inputTempo) <= 300
    );
  };

  const resetSectionFields = (form: IStyleTransferForm, sectionIndex?: number) => {
    const resetFields: Partial<StyleSection> = {
      styleId: undefined,
      genre: undefined,
      scale: undefined,
      tempo: undefined,
      sectionName: undefined,
      source: undefined,
      pitch: undefined,
      durationBar: undefined,
      annotator: undefined,
    };

    if (typeof sectionIndex === "number") {
      return {
        ...form,
        sections: form.sections.map((section, i) => (i === sectionIndex ? { ...section, ...resetFields } : section)),
      };
    } else {
      return {
        ...form,
        sections: form.sections.map((section) => ({
          ...section,
          ...resetFields,
        })),
      };
    }
  };

  const handleMainFormInputChange = (e: ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    let updatedForm = { ...form, [name]: value.trim() };

    // If MIDI file is present and inputTimeSignature or inputTempo is updated, reset MIDI and sections
    if (form.inputMidiPath && (name === "inputTimeSignature" || name === "inputTempo")) {
      updatedForm.inputMidiPath = "";
      updatedForm.sections = [];
    }

    if (name === "inputTimeSignature") {
      updatedForm = resetSectionFields(updatedForm);
    }
    setForm(updatedForm);
    setIsMainInputFormFilled(getMainInputFillStatus(updatedForm));
  };

  const handleOutputDirectoryChange = async () => {
    try {
      const dirPath = await open({
        directory: true,
        multiple: false,
      });

      if (typeof dirPath === "string") {
        let updatedForm = { ...form, outputDir: dirPath };
        setForm(updatedForm);
        setIsMainInputFormFilled(getMainInputFillStatus(updatedForm));
      }
    } catch (err) {
      console.error("Error selecting directory:", err);
    }
  };

  const handleMidiUpload = async () => {
    const filePath = await open({
      directory: false,
      multiple: false,
      filters: [{ name: "ZIP Files", extensions: ["zip"] }],
    });

    if (!filePath) return;
    if (!filePath.endsWith(".zip")) {
      alert("Please upload a valid Midi ZIP file.");
      return;
    }
    setIsMidiLoading(true);
    try {
      const { sections } = await StyleAPIService.getMidiInfo({
        midi_path: filePath,
        time_signature: form.inputTimeSignature,
        tempo: form.inputTempo,
      });

      if (sections.length < 1) {
        alert("No Markers found in MIDI, Please select a MIDI with marker.");
        setIsMidiLoading(false);
        return;
      }
      let updatedForm = {
        ...form,
        inputMidiPath: filePath,
        sections: sections.map((section: any, index: number) => ({
          slNo: index + 1,
          isBlank: false,
          midiSection: section.name,
          startBar: section.start_bar || "",
          startBeat: section.start_beat || "",
          endBar: section.end_bar || "",
          endBeat: section.end_beat || "",
        })),
      };

      setForm(updatedForm);
      await commons.sleep(200);
      setIsMainInputFormFilled(getMainInputFillStatus(updatedForm));
    } catch (error) {
      console.error(error);
      alert("Failed to upload MIDI file");
    }
    setIsMidiLoading(false);
  };

  const handleSwingToggle = (e: ChangeEvent<HTMLInputElement>) => {
    let updatedForm = { ...form, inputSwing: e.target.checked };
    updatedForm = resetSectionFields(updatedForm);
    setForm(updatedForm);
  };

  const handleSectionItemSelect = (item: string | null, sectionIndex: number, key: string) => {
    const itemValue = item?.trim() ?? undefined;
    const updatedForm = {
      ...form,
      sections: form.sections.map((section, i) =>
        i === sectionIndex
          ? {
            ...section,
            [key]: itemValue,
            styleId: itemValue ? section.styleId : undefined,
          }
          : section
      ),
    };
    setForm(updatedForm);
  };

  const handleStyleAudioItemSelect = (item: StyleBean, sectionIndex: number) => {
    const updatedForm = {
      ...form,
      projectRefId: item.project_ref_id,
      sections: form.sections.map((section, i) =>
        i === sectionIndex
          ? {
            ...section,
            genre: item.genre,
            scale: item.scale,
            tempo: item.tempo,
            sectionName: item.section,
            source: item.source,
            styleId: item._id,
            pitch: item.pitch,
            durationBar: item.duration_in_bars,
            annotator: item.annotator,
          }
          : section
      ),
    };
    setForm(updatedForm);
  };

  const handleIsBlankToggle = (isBlank: boolean, index: number) => {
    setForm((prev) => ({
      ...prev,
      sections: prev.sections.map((section, i) =>
        i === index
          ? {
            ...section,
            isBlank: isBlank,
            styleId: undefined,
            genre: undefined,
            source: undefined,
            scale: undefined,
            tempo: undefined,
            sectionName: undefined,
            pitch: undefined,
            durationBar: undefined,
            annotator: undefined,
          }
          : section
      ),
    }));
  };

  function createFilterBody(key: string, index: number) {
    let filter: any = {
      time_signature: form.inputTimeSignature,
      swing: form.inputSwing,
      genre: form.sections[index].genre,
      scale: form.sections[index].scale,
      tempo: form.sections[index].tempo,
      section: form.sections[index].sectionName,
      source: form.sections[index].source,
      pitch: form.sections[index].pitch,
      annotator: form.sections[index].annotator,
      duration_in_bars: form.sections[index].durationBar,
    };
    commons.removeFalsyProps(filter);
    delete filter[key];
    return filter;
  }

  const getSectionGenres = async (sectionIndex: number) => {
    const data = await StyleAPIService.getGenres(createFilterBody("genre", sectionIndex));
    return { data, hasMore: false };
  };
  const getSectionScales = async (sectionIndex: number) => {
    const data = await StyleAPIService.getScales(createFilterBody("scale", sectionIndex));
    return { data, hasMore: false };
  };
  const getSectionPitches = async (sectionIndex: number) => {
    const data = await StyleAPIService.getPitches(createFilterBody("pitch", sectionIndex));
    return { data, hasMore: false };
  };
  const getSectionDurationBars = async (sectionIndex: number) => {
    const data = await StyleAPIService.getDurations(createFilterBody("duration_in_bars", sectionIndex));
    return { data, hasMore: false };
  };
  const getSectionNames = async (sectionIndex: number) => {
    const data = await StyleAPIService.getSections(createFilterBody("section", sectionIndex));
    return { data, hasMore: false };
  };
  const getSectionSources = async (sectionIndex: number) => {
    const data = await StyleAPIService.getSources(createFilterBody("source", sectionIndex));
    return { data, hasMore: false };
  };
  const getSectionTempos = async (sectionIndex: number) => {
    const data = await StyleAPIService.getTempos(createFilterBody("tempo", sectionIndex));
    return { data, hasMore: false };
  };
  const getSectionAnnotators = async (sectionIndex: number) => {
    const data = await StyleAPIService.getAnnotators(createFilterBody("annotator", sectionIndex));
    return { data, hasMore: false };
  };

  const getStyles = async (page: number, pageSize: number, sectionIndex: number) => {
    const { styles, paginated_cursor } = await StyleAPIService.getStyles(createFilterBody("", sectionIndex), { page, pageSize });
    return { data: styles, hasMore: paginated_cursor["has_next"] || false };
  };

  // --------------------------------------
  // Submit entire form
  // --------------------------------------

  const handleFormReset = () => {
    setForm({
      inputSongName: "",
      inputTimeSignature: "",
      inputSwing: false,
      inputGenre: "",
      inputScale: "",
      inputTempo: "",
      inputMidiPath: "",
      inputAnnotator: "",
      inputPitch: "",
      outputDir: "",
      sections: [],
    });
    setIsMainInputFormFilled(false);
  };

  async function handleSubmit(e: FormEvent) {
    e.preventDefault();
    setIsLoaderOpen(true);
    setLoaderMessage("Transferring style...");
    setLoaderProgress(10);
    try {
      const finalData = {
        time_signature: form.inputTimeSignature,
        swing: form.inputSwing,
        genre: form.inputGenre,
        scale: form.inputScale,
        tempo: form.inputTempo,
        song_name: form.inputSongName,
        input_midi_zip_path: form.inputMidiPath,
        output_directory: form.outputDir,
        annotator: form.inputAnnotator,
        pitch: form.inputPitch,
        sections: form.sections.map((item) => {
          return {
            input_midi_section: item.midiSection,
            genre: item.genre,
            source: item.source,
            section: item.sectionName,
            style_id: item.styleId,
            is_blank: item.isBlank,
            sl_no: item.slNo,
            start_bar: item.startBar,
            start_beat: item.startBeat,
            end_bar: item.endBar,
            end_beat: item.endBeat,
          };
        }),
      };
      setLoaderProgress(30);
      await StyleAPIService.styleTransfer(finalData);
      setLoaderProgress(100);
      setLoaderMessage("Style transfer complete!");
      setShowSuccessDialog(true);
    } catch (error: any) {
      console.error(error);
      alert(`Error submitting style transfer form: ${error.message || error}`);
    } finally {
      setIsLoaderOpen(false);
    }
  }

  const handleSuccessDialogClose = () => {
    setShowSuccessDialog(false);
    commons.hardResfresh();
  };

  const showFormResetConfirmation = () => {
    setConfirmationDialogConfig({
      title: "Reset",
      message: "Are you sure you want to reset this style ?",
      onConfirm: async () => {
        setIsConfirmationDialogOpen(false);
        handleFormReset();
      },
    });
    setIsConfirmationDialogOpen(true);
  };

  useEffect(() => {
    const fetchGenreList = async () => {
      try {
        const genres: string[] = await StyleAPIService.getConfigGenres();
        setGenreList(genres);
      } catch (error: any) {
        alert(`Error fetching genres: ${error.message}`);
      }
    };
    fetchGenreList();
  }, []);

  // Render
  // --------------------------------------
  return (
    <motion.div
      className="min-h-screen bg-gradient-to-br from-melodyze-secondary via-melodyze-tertiary to-black text-gray-100 font-inter"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
    >
      <FullScreenLoader isOpen={isLoaderOpen} message={loaderMessage} progress={loaderProgress} />
      <SuccessDialog
        isOpen={showSuccessDialog}
        message={"Your style has been transferred successfully."}
        onConfirm={handleSuccessDialogClose}
      />
      <ConfirmationDialog
        isOpen={isConfirmationDialogOpen}
        title={confirmationDialogConfig.title}
        message={confirmationDialogConfig.message}
        onConfirm={confirmationDialogConfig.onConfirm}
        onCancel={() => setIsConfirmationDialogOpen(false)}
      />

      {/* Header */}
      <div className="container mx-auto px-6 py-8">
        {/* Header */}
        <motion.div
          className="flex justify-between items-center mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <motion.h1
            className="text-4xl font-iceland font-bold flex items-center bg-gradient-to-r from-melodyze-cyan via-melodyze-purple to-melodyze-pink bg-clip-text text-transparent"
            whileHover={{ scale: 1.02 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <motion.div whileHover={{ rotate: 15 }} transition={{ type: "spring", stiffness: 300 }}>
              <GuitarIcon className="mr-3 text-melodyze-cyan h-8 w-8" />
            </motion.div>
            Transfer Styles
          </motion.h1>
        </motion.div>

        <motion.div
          className="bg-melodyze-secondary/30 backdrop-blur-xl border border-melodyze-cyan/20 rounded-2xl overflow-hidden shadow-2xl relative"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.3, delay: 0.4 }}
            className="absolute top-6 right-6 z-10"
          >
            {isMainInputFormFilled ? (
              <CheckCircle2 className="text-green-400 w-7 h-7 drop-shadow-lg" />
            ) : (
              <AlertCircle className="text-yellow-400 w-7 h-7 cursor-pointer drop-shadow-lg" />
            )}
          </motion.div>

          <form onSubmit={handleSubmit} className="p-8 space-y-10">
            {/* Basic Configuration Section */}
            <motion.div
              className="space-y-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <motion.h2
                className="text-2xl font-iceland font-bold text-white flex items-center bg-gradient-to-r from-melodyze-cyan to-melodyze-purple bg-clip-text text-transparent"
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <motion.div whileHover={{ rotate: 180 }} transition={{ duration: 0.3 }}>
                  <SettingsIcon className="mr-3 text-melodyze-cyan h-6 w-6" />
                </motion.div>
                Basic Configuration
              </motion.h2>

              <motion.div
                className="grid grid-cols-1 md:grid-cols-3 gap-8"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.4, staggerChildren: 0.1 }}
              >
                {/* Time Signature */}
                <motion.div
                  className="space-y-3"
                  variants={{
                    hidden: { opacity: 0, y: 20 },
                    visible: { opacity: 1, y: 0 },
                  }}
                >
                  <div className="flex items-center space-x-3">
                    <ClockIcon className="text-melodyze-cyan h-5 w-5" />
                    <label htmlFor="timeSignature" className="text-sm font-inter font-medium text-gray-300">
                      Time Signature <span className="text-melodyze-pink">*</span>
                    </label>
                  </div>
                  <motion.div className="relative" whileHover={{ scale: 1.02 }} transition={{ type: "spring", stiffness: 300 }}>
                    <select
                      id="timeSignature"
                      name="inputTimeSignature"
                      className={`w-full bg-melodyze-secondary/50 backdrop-blur-sm border-2 ${form.inputTimeSignature ? "border-melodyze-cyan/50" : "border-melodyze-cyan/20"
                        } rounded-xl py-3 px-4 text-sm font-inter text-gray-200 focus:outline-none focus:ring-2 focus:ring-melodyze-cyan/50 focus:border-melodyze-cyan transition-all duration-300 appearance-none`}
                      value={form.inputTimeSignature || ""}
                      onChange={handleMainFormInputChange}
                      required
                    >
                      <option value="" disabled>
                        Select time signature
                      </option>
                      {TimeSignList.map((ts, i) => (
                        <option key={i} value={TimeSignKeyMap[ts]}>
                          {ts}
                        </option>
                      ))}
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
                      <svg className="h-5 w-5 text-melodyze-cyan/70" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </motion.div>
                </motion.div>

                {/* Tempo */}
                <motion.div
                  className="space-y-3"
                  variants={{
                    hidden: { opacity: 0, y: 20 },
                    visible: { opacity: 1, y: 0 },
                  }}
                >
                  <div className="flex items-center space-x-3">
                    <GaugeCircle className="text-melodyze-purple h-5 w-5" />
                    <label htmlFor="tempo" className="text-sm font-inter font-medium text-gray-300">
                      Tempo <span className="text-melodyze-pink">*</span>
                    </label>
                  </div>
                  <motion.input
                    id="tempo"
                    type="number"
                    name="inputTempo"
                    className={`w-full bg-melodyze-secondary/50 backdrop-blur-sm border-2 ${form.inputTempo ? "border-melodyze-purple/50" : "border-melodyze-purple/20"
                      } rounded-xl py-3 px-4 text-sm font-inter text-gray-200 focus:outline-none focus:ring-2 focus:ring-melodyze-purple/50 focus:border-melodyze-purple transition-all duration-300`}
                    min={60}
                    max={300}
                    value={form.inputTempo}
                    onChange={handleMainFormInputChange}
                    onWheel={(e) => e.currentTarget.blur()}
                    placeholder="Enter tempo (60-300)"
                    required
                    whileHover={{ scale: 1.02 }}
                    whileFocus={{ scale: 1.02 }}
                    transition={{ type: "spring", stiffness: 300 }}
                  />
                </motion.div>

                {/* Swing */}
                <motion.div
                  className="space-y-3"
                  variants={{
                    hidden: { opacity: 0, y: 20 },
                    visible: { opacity: 1, y: 0 },
                  }}
                >
                  <div className="flex items-center space-x-3">
                    <MdOutlineSwipe className="text-melodyze-pink h-5 w-5" />
                    <label className="text-sm font-inter font-medium text-gray-300">Swing</label>
                  </div>
                  <div className="flex items-center">
                    <motion.label
                      className="relative inline-flex items-center cursor-pointer"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <input type="checkbox" className="sr-only peer" checked={form.inputSwing} onChange={handleSwingToggle} />
                      <motion.div
                        className={`w-14 h-7 rounded-full relative transition-all duration-300 ${form.inputSwing
                          ? "bg-gradient-to-r from-melodyze-pink to-melodyze-purple"
                          : "bg-melodyze-secondary/50 border-2 border-melodyze-pink/30"
                          }`}
                        animate={{
                          backgroundColor: form.inputSwing ? "#ec4899" : "#1f2937",
                        }}
                        transition={{ type: "spring", stiffness: 300 }}
                      >
                        <motion.div
                          className="absolute top-1 left-1 w-5 h-5 bg-white rounded-full shadow-lg"
                          animate={{
                            x: form.inputSwing ? 24 : 0,
                            rotate: form.inputSwing ? 360 : 0,
                          }}
                          transition={{ type: "spring", stiffness: 300, damping: 20 }}
                        />
                      </motion.div>
                    </motion.label>
                  </div>
                </motion.div>
              </motion.div>
            </motion.div>

            {/* Musical Properties Section */}
            <motion.div
              className="space-y-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.5 }}
            >
              <motion.h2
                className="text-2xl font-iceland font-bold text-white flex items-center bg-gradient-to-r from-melodyze-purple to-melodyze-pink bg-clip-text text-transparent"
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <motion.div whileHover={{ rotate: 15, scale: 1.1 }} transition={{ duration: 0.3 }}>
                  <MusicIcon className="mr-3 text-melodyze-purple h-6 w-6" />
                </motion.div>
                Musical Properties
              </motion.h2>

              <motion.div
                className="grid grid-cols-1 md:grid-cols-3 gap-8"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.6, staggerChildren: 0.1 }}
              >
                {/* Genre */}
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <TagIcon className="text-indigo-400 h-4 w-4" />
                    <label htmlFor="genre" className="text-sm font-medium text-gray-300">
                      Genre <span className="text-red-500">*</span>
                    </label>
                  </div>
                  <div className="relative">
                    <select
                      id="genre"
                      className={`w-full bg-gray-900/70 border ${form.inputGenre ? "border-cyan-500" : "border-gray-700"
                        } rounded-md py-2 px-3 text-sm text-gray-200 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent appearance-none`}
                      defaultValue={form.inputGenre || ""}
                      onChange={handleMainFormInputChange}
                      name="inputGenre"
                      required
                    >
                      <option value="" disabled>
                        Select a genre
                      </option>
                      {genreList.map((val, index) => (
                        <option key={index} value={val}>
                          {val}
                        </option>
                      ))}
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                      <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </div>
                </div>

                {/* Key */}
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <KeyRoundIcon className="text-indigo-400 h-4 w-4" />
                    <label htmlFor="pitch" className="text-sm font-medium text-gray-300">
                      Key <span className="text-red-500">*</span>
                    </label>
                  </div>
                  <div className="relative">
                    <select
                      id="pitch"
                      name="inputPitch"
                      className={`w-full bg-gray-900/70 border ${form.inputPitch ? "border-cyan-500" : "border-gray-700"
                        } rounded-md py-2 px-3 text-sm text-gray-200 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent appearance-none`}
                      value={form.inputPitch}
                      onChange={handleMainFormInputChange}
                      required
                    >
                      <option value="" disabled>
                        Select a key
                      </option>
                      {SongScaleList.map((sc, i) => (
                        <option key={i} value={SongScaleKeyMap[sc]}>
                          {sc}
                        </option>
                      ))}
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                      <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </div>
                </div>

                {/* Scale */}
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <VscSymbolRuler className="text-indigo-400 h-4 w-4" />
                    <label htmlFor="scale" className="text-sm font-medium text-gray-300">
                      Scale <span className="text-red-500">*</span>
                    </label>
                  </div>
                  <div className="relative">
                    <select
                      id="scale"
                      name="inputScale"
                      className={`w-full bg-gray-900/70 border ${form.inputScale ? "border-cyan-500" : "border-gray-700"
                        } rounded-md py-2 px-3 text-sm text-gray-200 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent appearance-none`}
                      value={form.inputScale || ""}
                      onChange={handleMainFormInputChange}
                      required
                    >
                      <option value="" disabled>
                        Select a scale
                      </option>
                      {SongPitchList.map((p, i) => (
                        <option key={i} value={SongPitchKeyMap[p]}>
                          {p}
                        </option>
                      ))}
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                      <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </div>
                </div>
              </motion.div>
            </motion.div>

            {/* Project Information Section */}
            <motion.div
              className="space-y-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.7 }}
            >
              <motion.h2
                className="text-2xl font-iceland font-bold text-white flex items-center bg-gradient-to-r from-melodyze-pink to-melodyze-indigo bg-clip-text text-transparent"
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <motion.div whileHover={{ rotate: -15, scale: 1.1 }} transition={{ duration: 0.3 }}>
                  <FileMusicIcon className="mr-3 text-melodyze-pink h-6 w-6" />
                </motion.div>
                Project Information
              </motion.h2>

              <motion.div
                className="grid grid-cols-1 md:grid-cols-2 gap-8"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.8, staggerChildren: 0.1 }}
              >
                {/* Song Name */}
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <MusicIcon className="text-indigo-400 h-4 w-4" />
                    <label className="text-sm font-medium text-gray-300">
                      Song Name <span className="text-red-500">*</span>
                    </label>
                  </div>
                  <input
                    type="text"
                    name="inputSongName"
                    className={`w-full bg-gray-900/70 border ${form.inputSongName ? "border-cyan-500" : "border-gray-700"
                      } rounded-md py-2 px-3 text-sm text-gray-200 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent`}
                    value={form.inputSongName}
                    onChange={handleMainFormInputChange}
                    placeholder="Enter song name"
                    required
                  />
                </div>

                {/* Annotator */}
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <UserIcon className="text-indigo-400 h-4 w-4" />
                    <label htmlFor="annotator" className="text-sm font-medium text-gray-300">
                      Annotator <span className="text-red-500">*</span>
                    </label>
                  </div>
                  <div className="relative">
                    <select
                      id="annotator"
                      name="inputAnnotator"
                      className={`w-full bg-gray-900/70 border ${form.inputAnnotator ? "border-cyan-500" : "border-gray-700"
                        } rounded-md py-2 px-3 text-sm text-gray-200 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent appearance-none`}
                      value={form.inputAnnotator}
                      onChange={handleMainFormInputChange}
                      required
                    >
                      <option value="" disabled>
                        Select annotator
                      </option>
                      {AnnotatorList.map((name, i) => (
                        <option key={i} value={name}>
                          {name}
                        </option>
                      ))}
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                      <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </div>
                </div>
              </motion.div>
            </motion.div>

            {/* File Upload Section */}
            <motion.div
              className="space-y-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.9 }}
            >
              <motion.h2
                className="text-2xl font-iceland font-bold text-white flex items-center bg-gradient-to-r from-melodyze-indigo to-melodyze-cyan bg-clip-text text-transparent"
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <motion.div whileHover={{ rotate: 360, scale: 1.1 }} transition={{ duration: 0.5 }}>
                  <UploadIcon className="mr-3 text-melodyze-indigo h-6 w-6" />
                </motion.div>
                File Upload
              </motion.h2>

              <motion.div
                className="grid grid-cols-1 md:grid-cols-2 gap-8"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 1.0, staggerChildren: 0.1 }}
              >
                {/* Output Directory */}
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <FolderOutputIcon className="text-indigo-400 h-4 w-4" />
                    <label htmlFor="outputDirectory" className="text-sm font-medium text-gray-300">
                      Output Directory <span className="text-red-500">*</span>
                    </label>
                  </div>
                  <button
                    type="button"
                    onClick={handleOutputDirectoryChange}
                    className={`w-full flex items-center justify-between bg-gray-900/70 border ${form.outputDir ? "border-cyan-500" : "border-gray-700"
                      } rounded-md py-2 px-3 text-sm text-gray-200 hover:bg-gray-700/70 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-cyan-500`}
                  >
                    <span className="flex items-center truncate">
                      <FolderOpenIcon className="mr-2 h-4 w-4 text-indigo-400" />
                      <span className="truncate">{form.outputDir || "Select Output Folder"}</span>
                    </span>
                    <span className="text-gray-400 ml-2">
                      <ArrowRight className="h-4 w-4" />
                    </span>
                  </button>
                </div>

                {/* Upload MIDI */}
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <FileArchive className="text-indigo-400 h-4 w-4" />
                    <label className="text-sm font-medium text-gray-300">
                      MIDI File (.zip) <span className="text-red-500">*</span>
                    </label>
                  </div>
                  <button
                    type="button"
                    onClick={handleMidiUpload}
                    className={`w-full flex items-center justify-between bg-gray-900/70 border ${form.inputMidiPath ? "border-cyan-500" : "border-gray-700"
                      } rounded-md py-2 px-3 text-sm text-gray-200 hover:bg-gray-700/70 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-cyan-500 ${!form.inputTimeSignature || !form.inputTempo ? "opacity-50 cursor-not-allowed" : ""
                      }`}
                    disabled={!form.inputTimeSignature || !form.inputTempo}
                  >
                    <span className="flex items-center truncate">
                      <FileArchive className="mr-2 h-4 w-4 text-indigo-400" />
                      <span className="truncate">{form.inputMidiPath || "Browse a file"}</span>
                    </span>
                    <span className="text-gray-400 ml-2">
                      <ArrowRight className="h-4 w-4" />
                    </span>
                  </button>
                </div>
              </motion.div>
            </motion.div>

            {/* Top-level main form ends here */}

            {/* Dynamic MIDI Sections */}
            <AnimatePresence>
              {form.sections && form.sections.length > 0 && (
                <motion.div
                  className="space-y-6"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.5, delay: 1.1 }}
                >
                  <motion.h2
                    className="text-2xl font-iceland font-bold text-white flex items-center bg-gradient-to-r from-melodyze-cyan to-melodyze-purple bg-clip-text text-transparent"
                    whileHover={{ scale: 1.02 }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <motion.div whileHover={{ scale: 1.2 }} transition={{ duration: 0.3 }}>
                      <Volume2 className="mr-3 text-melodyze-cyan h-6 w-6" />
                    </motion.div>
                    MIDI Sections
                  </motion.h2>
                </motion.div>
              )}
            </AnimatePresence>

            <div className="space-y-4 max-h-[700px] overflow-y-auto">
              {isMidiLoading && (
                <div className="space-y-4">
                  {Array.from({ length: 3 }).map((_, index) => (
                    <div key={index} className="bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-lg p-6 animate-pulse">
                      <div className="h-4 bg-gray-600 rounded w-1/4 mb-4"></div>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        {Array.from({ length: 8 }).map((_, i) => (
                          <div key={i} className="h-10 bg-gray-600 rounded"></div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {form.sections &&
                form.sections.map((section, index) => {
                  const fetchSectionGenres = async (page: number, pageSize: number) => {
                    return getSectionGenres(index);
                  };
                  const fetchSectionScales = async (page: number, pageSize: number) => {
                    return getSectionScales(index);
                  };
                  const fetchSectionPitches = async (page: number, pageSize: number) => {
                    return getSectionPitches(index);
                  };
                  const fetchDurationBars = async (page: number, pageSize: number) => {
                    return getSectionDurationBars(index);
                  };
                  const fetchSectionNames = async (page: number, pageSize: number) => {
                    return getSectionNames(index);
                  };
                  const fetchSectionSources = async (page: number, pageSize: number) => {
                    return getSectionSources(index);
                  };
                  const fetchSectionTempos = async (page: number, pageSize: number) => {
                    return getSectionTempos(index);
                  };
                  const fetchSectionAnnotators = async (page: number, pageSize: number) => {
                    return getSectionAnnotators(index);
                  };

                  const onGenreSelect = (item: string | null) => {
                    handleSectionItemSelect(item, index, "genre");
                  };
                  const onScaleSelect = (item: string | null) => {
                    handleSectionItemSelect(item, index, "scale");
                  };
                  const onPitchSelect = (item: string | null) => {
                    handleSectionItemSelect(item, index, "pitch");
                  };
                  const onDurationBarSelect = (item: string | null) => {
                    handleSectionItemSelect(item, index, "durationBar");
                  };
                  const onTempoSelect = (item: string | null) => {
                    handleSectionItemSelect(item, index, "tempo");
                  };
                  const onSourceSelect = (item: string | null) => {
                    handleSectionItemSelect(item, index, "source");
                  };
                  const onSectionNameSelect = (item: string | null) => {
                    handleSectionItemSelect(item, index, "sectionName");
                  };
                  const onAnnotatorSelect = (item: string | null) => {
                    handleSectionItemSelect(item, index, "annotator");
                  };

                  async function fetchStyleAudioItems(page: number, pageSize: number): Promise<FetchStyleResult> {
                    return getStyles(page, pageSize, index);
                  }

                  const onStyleAudioItemSelect = (item: StyleBean) => {
                    handleStyleAudioItemSelect(item, index);
                  };

                  return (
                    <motion.div
                      key={index}
                      className="bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-lg overflow-hidden shadow-lg relative"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                    >
                      {/* Section Header */}
                      <div className="bg-gradient-to-r from-gray-800/50 to-gray-700/50 p-4 border-b border-gray-700">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="bg-gradient-to-r from-cyan-500 to-blue-500 text-white px-3 py-1 rounded-md text-sm font-medium shadow-lg">
                              {section.slNo}. {section.midiSection || "Unknown Name"}
                            </div>
                            {section.isBlank && <CheckCircle2 className="text-green-500 w-5 h-5" />}
                          </div>

                          <div className="flex items-center space-x-3">
                            {!section.isBlank && (
                              <>
                                <SelectAudioItemDialog
                                  fetchData={fetchStyleAudioItems}
                                  onSelect={onStyleAudioItemSelect}
                                  pageSize={10}
                                  openButtonLabel={section.styleId ? "Style Selected" : "Listen and select style"}
                                  title="Listen and Select Style"
                                  appliedFilter={createFilterBody("", index)}
                                  warning={!section.styleId}
                                />
                                <Button
                                  type="button"
                                  variant="outline"
                                  onClick={() => handleIsBlankToggle(false, index)}
                                  className="text-yellow-400 border-yellow-400 hover:bg-yellow-400/10"
                                  disabled={section.isBlank}
                                >
                                  <RefreshCw className="mr-2 h-4 w-4" />
                                  Reset
                                </Button>
                              </>
                            )}

                            {/* Blank Section Toggle */}
                            <div className="flex items-center space-x-2 bg-gray-800/50 px-3 py-2 rounded-md border border-gray-600">
                              <label className="text-sm font-medium text-gray-300">Blank Section</label>
                              <label className="inline-flex items-center cursor-pointer">
                                <input
                                  type="checkbox"
                                  className="sr-only"
                                  checked={section.isBlank}
                                  onChange={(e) => handleIsBlankToggle(e.target.checked, index)}
                                />
                                <div
                                  className={`w-11 h-6 rounded-full relative transition-all duration-300 ${section.isBlank ? "bg-cyan-600" : "bg-gray-600"
                                    }`}
                                >
                                  <div
                                    className={`absolute top-0.5 left-0.5 w-5 h-5 bg-white rounded-full shadow-md transition-all ${section.isBlank ? "translate-x-5" : ""
                                      }`}
                                  ></div>
                                </div>
                              </label>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Section Content */}
                      <div className="p-6">
                        {!section.isBlank && (
                          <div className="space-y-6">
                            {/* Section Timing */}
                            <div className="space-y-3">
                              <div className="grid grid-cols-2 md:grid-cols-5 gap-4 items-end">
                                <div className="space-y-2">
                                  <label className="text-xs font-medium text-gray-300">Start Bar</label>
                                  <input
                                    type="number"
                                    name="startBar"
                                    className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-sm text-gray-300"
                                    value={section.startBar}
                                    disabled
                                  />
                                </div>
                                <div className="space-y-2">
                                  <label className="text-xs font-medium text-gray-300">Start Beat</label>
                                  <input
                                    type="number"
                                    name="startBeat"
                                    className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-sm text-gray-300"
                                    value={section.startBeat}
                                    disabled
                                  />
                                </div>
                                <div className="flex items-center justify-center pb-2">
                                  <span className="text-indigo-400 font-bold text-lg">:</span>
                                </div>
                                <div className="space-y-2">
                                  <label className="text-xs font-medium text-gray-300">End Bar</label>
                                  <input
                                    type="number"
                                    name="endBar"
                                    className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-sm text-gray-300"
                                    value={section.endBar}
                                    disabled
                                  />
                                </div>
                                <div className="space-y-2">
                                  <label className="text-xs font-medium text-gray-300">End Beat</label>
                                  <input
                                    type="number"
                                    name="endBeat"
                                    className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-sm text-gray-300"
                                    value={section.endBeat}
                                    disabled
                                  />
                                </div>
                              </div>
                            </div>

                            {/* Section Filters */}
                            <div className="space-y-3">
                              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                                <div className="space-y-2">
                                  <label className="text-xs font-medium text-gray-300">
                                    Annotator <span className="text-red-500">*</span>
                                  </label>
                                  <PaginatedDropdown
                                    value={section.annotator}
                                    fetchData={fetchSectionAnnotators}
                                    onSelect={onAnnotatorSelect}
                                    placeholder="Select an Annotator"
                                  />
                                </div>

                                <div className="space-y-2">
                                  <label className="text-xs font-medium text-gray-300">
                                    Genre <span className="text-red-500">*</span>
                                  </label>
                                  <PaginatedDropdown
                                    value={section.genre}
                                    fetchData={fetchSectionGenres}
                                    onSelect={onGenreSelect}
                                    placeholder="Select a genre"
                                  />
                                </div>

                                <div className="space-y-2">
                                  <label className="text-xs font-medium text-gray-300">
                                    Section <span className="text-red-500">*</span>
                                  </label>
                                  <PaginatedDropdown
                                    value={section.sectionName}
                                    fetchData={fetchSectionNames}
                                    onSelect={onSectionNameSelect}
                                    placeholder="Select a Section"
                                  />
                                </div>

                                <div className="space-y-2">
                                  <label className="text-xs font-medium text-gray-300">
                                    Source <span className="text-red-500">*</span>
                                  </label>
                                  <PaginatedDropdown
                                    value={section.source}
                                    fetchData={fetchSectionSources}
                                    onSelect={onSourceSelect}
                                    placeholder="Select a source"
                                  />
                                </div>
                              </div>

                              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                                <div className="space-y-2">
                                  <label className="text-xs font-medium text-gray-300">
                                    Key <span className="text-red-500">*</span>
                                  </label>
                                  <PaginatedDropdown
                                    value={section.pitch}
                                    fetchData={fetchSectionPitches}
                                    onSelect={onPitchSelect}
                                    placeholder="Select a key"
                                  />
                                </div>

                                <div className="space-y-2">
                                  <label className="text-xs font-medium text-gray-300">
                                    Scale <span className="text-red-500">*</span>
                                  </label>
                                  <PaginatedDropdown
                                    value={section.scale}
                                    fetchData={fetchSectionScales}
                                    onSelect={onScaleSelect}
                                    placeholder="Select a scale"
                                  />
                                </div>

                                <div className="space-y-2">
                                  <label className="text-xs font-medium text-gray-300">
                                    Tempo <span className="text-red-500">*</span>
                                  </label>
                                  <PaginatedDropdown
                                    value={section.tempo}
                                    fetchData={fetchSectionTempos}
                                    onSelect={onTempoSelect}
                                    placeholder="Select a tempo"
                                  />
                                </div>

                                <div className="space-y-2">
                                  <label className="text-xs font-medium text-gray-300">
                                    Duration in Bars <span className="text-red-500">*</span>
                                  </label>
                                  <PaginatedDropdown
                                    value={section.durationBar}
                                    fetchData={fetchDurationBars}
                                    onSelect={onDurationBarSelect}
                                    placeholder="Select a Duration"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    </motion.div>
                  );
                })}
            </div>
            {/* ---------- Dynamic Child Sections Ends here---------- */}

            {/* Form Actions */}
            <motion.div
              className="flex flex-col sm:flex-row justify-end space-y-4 sm:space-y-0 sm:space-x-6 pt-8 border-t border-melodyze-cyan/20"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 1.2 }}
            >
              <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                <Button
                  type="submit"
                  className="px-10 py-3 flex items-center font-inter font-semibold bg-gradient-to-r from-melodyze-pink via-melodyze-purple to-melodyze-indigo transition-all duration-500 hover:from-melodyze-pink/80 hover:via-melodyze-purple/80 hover:to-melodyze-indigo/80 shadow-lg hover:shadow-melodyze-purple/25"
                  disabled={!(isMainInputFormFilled && form.sections.length > 0 && form.sections.every((e) => e.isBlank || !!e.styleId))}
                >
                  <PlayIcon className="mr-3 h-5 w-5" />
                  Start Transferring
                </Button>
              </motion.div>

              <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                <Button
                  type="button"
                  variant="outline"
                  className="px-8 py-3 font-inter font-medium text-gray-300 border-melodyze-cyan/30 hover:bg-melodyze-cyan/10 hover:border-melodyze-cyan/50 transition-all duration-300"
                  onClick={(e) => {
                    e.preventDefault();
                    showFormResetConfirmation();
                  }}
                >
                  <RotateCcw className="mr-3 h-4 w-4" />
                  Reset
                </Button>
              </motion.div>
            </motion.div>
          </form>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default StyleTransferPage;
