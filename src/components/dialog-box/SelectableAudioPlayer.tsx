import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { AiFillWarning } from "react-icons/ai";
import AudioPlayer from "@/components/audio-player/AudioPlayer";
import { IoMdCheckmarkCircle } from "react-icons/io";
import { StyleBean } from "@/models/Style";
import { X, CheckCircle2 } from "lucide-react";
import { createPortal } from "react-dom";

const widthMap: { [key: number]: number } = {
  0: 40,
  1: 15,
  2: 15,
  3: 5,
  4: 5,
  5: 10,
  6: 10,
};

function formatKeyLabel(key: string): string {
  return key
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
}

export interface FetchStyleResult {
  data: StyleBean[];
  hasMore: boolean;
}

interface SelectAudioItemDialogProps {
  fetchData: (page: number, pageSize: number) => Promise<FetchStyleResult>;
  onSelect?: (item: StyleBean) => void;
  pageSize?: number;
  openButtonLabel?: string;
  title?: string;
  appliedFilter: any;
  warning?: boolean;
}

const SelectAudioItemDialog: React.FC<SelectAudioItemDialogProps> = ({
  fetchData,
  onSelect,
  pageSize = 10,
  openButtonLabel = "Open Dialog",
  title = "Select an Item",
  appliedFilter,
  warning = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [items, setItems] = useState<StyleBean[]>([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  const [selectedItem, setSeletedItem] = useState<StyleBean | null>(null);
  const [loading, setLoading] = useState(false);

  const handleOpen = async () => {
    setIsOpen(true);
    setItems([]);
    setPage(1);
    setHasMore(false);
    setLoading(true);

    try {
      const result = await fetchData(1, pageSize);
      setItems(result.data);
      console.log("result.data:", result);
      setHasMore(result.hasMore);
    } catch (error) {
      console.error("Error fetching items:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setIsOpen(false);
    setSeletedItem(null);
  };

  const handleLoadMore = async () => {
    setLoading(true);
    const nextPage = page + 1;
    try {
      const result = await fetchData(nextPage, pageSize);
      setItems((prev) => [...prev, ...result.data]);
      setHasMore(result.hasMore);
      setPage(nextPage);
    } catch (error) {
      console.error("Error fetching more items:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleSelectClick = () => {
    if (!selectedItem) {
      alert("No style is selected.");
      return;
    }
    if (onSelect) onSelect(selectedItem);
    setSeletedItem(null);
    setIsOpen(false);
  };

  const renderSkeleton = () => {
    return Array.from({ length: pageSize }).map((_, idx) => (
      <motion.div
        key={idx}
        className="
          flex justify-between items-center
          px-6 py-4
          border-b border-melodyze-cyan/10
          animate-pulse
          bg-melodyze-secondary/30
          rounded-lg mx-2 mb-2
        "
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.3, delay: idx * 0.1 }}
      >
        <div className="flex items-center space-x-4">
          <div className="w-8 h-8 bg-melodyze-cyan/20 rounded-full"></div>
          <div className="space-y-2">
            <div className="w-32 h-4 bg-melodyze-cyan/20 rounded"></div>
            <div className="w-24 h-3 bg-melodyze-purple/20 rounded"></div>
          </div>
        </div>
        <div className="w-16 h-8 bg-melodyze-pink/20 rounded"></div>
      </motion.div>
    ));
  };


  return (
    <div>
      {/* Button that opens the dialog */}
      <motion.button
        type="button"
        onClick={handleOpen}
        className="flex items-center px-4 py-3 font-inter font-semibold mr-3 text-sm tracking-wide border border-melodyze-cyan/30 rounded-xl bg-gradient-to-b from-melodyze-secondary/80 to-melodyze-tertiary/80 shadow-lg transition-all hover:from-melodyze-secondary/60 hover:to-melodyze-tertiary/60 backdrop-blur-md text-gray-200 hover:text-white"
        whileHover={{ scale: 1.02, y: -1 }}
        whileTap={{ scale: 0.98 }}
        transition={{ type: "spring", stiffness: 300 }}
        style={{
          boxShadow: "0 8px 25px rgba(0, 0, 0, 0.3), 0 0 20px rgba(34, 211, 238, 0.1)",
        }}
      >
        <AnimatePresence mode="wait">
          {warning ? (
            <motion.div
              key="warning"
              initial={{ scale: 0, rotate: -180 }}
              animate={{ scale: 1, rotate: 0 }}
              exit={{ scale: 0, rotate: 180 }}
              transition={{ duration: 0.3 }}
            >
              <AiFillWarning title="Not Selected, Please select a style" className="mr-2 text-melodyze-pink w-4 h-4 cursor-pointer" />
            </motion.div>
          ) : (
            <motion.div
              key="success"
              initial={{ scale: 0, rotate: 180 }}
              animate={{ scale: 1, rotate: 0 }}
              exit={{ scale: 0, rotate: -180 }}
              transition={{ duration: 0.3 }}
            >
              <IoMdCheckmarkCircle className="text-melodyze-cyan mr-2 w-4 h-4" />
            </motion.div>
          )}
        </AnimatePresence>
        {openButtonLabel}
      </motion.button>

      <AnimatePresence>
        {isOpen &&
          createPortal(
            <motion.div
              className="fixed inset-0 z-[99999] flex items-center justify-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              <motion.div
                onClick={handleClose}
                className="absolute inset-0 bg-black/80 backdrop-blur-md"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
              />

              <motion.div
                className="
                relative
                max-w-6xl max-h-[90vh] w-[92%] md:w-[80%]
                bg-melodyze-secondary/95
                backdrop-blur-xl
                border border-melodyze-cyan/20
                rounded-2xl shadow-2xl p-2 sm:p-4 md:p-6
                flex flex-col
                font-inter
                overflow-hidden
              "
                initial={{ scale: 0.8, opacity: 0, y: 50 }}
                animate={{ scale: 1, opacity: 1, y: 0 }}
                exit={{ scale: 0.8, opacity: 0, y: 50 }}
                transition={{ type: "spring", stiffness: 300, damping: 25 }}
                style={{
                  boxShadow: "0 25px 50px rgba(0, 0, 0, 0.5), 0 0 50px rgba(34, 211, 238, 0.1)",
                  zIndex: 100000,
                }}
              >
                {/* Close Button */}
                <motion.button
                  onClick={handleClose}
                  className="
                  absolute
                  top-4 right-6
                  cursor-pointer
                  text-melodyze-cyan hover:text-melodyze-pink
                  transition-colors duration-300
                  z-10
                  p-2 rounded-full
                  hover:bg-melodyze-tertiary/30
                "
                  whileHover={{ scale: 1.1, rotate: 90 }}
                  whileTap={{ scale: 0.9 }}
                  transition={{ duration: 0.2 }}
                >
                  <X className="w-6 h-6" />
                </motion.button>
                {/* Top Area: Title & subTitle (15%) */}
                <motion.div
                  className="flex-shrink-0"
                  style={{ flexBasis: "8%" }}
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                >
                  <motion.h2
                    className="text-center text-2xl font-iceland font-bold bg-gradient-to-r from-melodyze-cyan to-melodyze-purple bg-clip-text text-transparent"
                    whileHover={{ scale: 1.02 }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    {title}
                  </motion.h2>
                  {/* Selected Filter Tabs */}
                  <motion.div
                    className="flex flex-wrap gap-3 justify-center mt-4"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.5, delay: 0.3, staggerChildren: 0.1 }}
                  >
                    {appliedFilter &&
                      Object.entries(appliedFilter).map(([key, value], index) => (
                        <motion.div
                          key={key}
                          className="px-3 py-2 rounded-lg bg-melodyze-secondary/60 text-gray-200 text-sm font-medium border border-melodyze-cyan/30 select-none backdrop-blur-sm"
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ duration: 0.3, delay: 0.4 + index * 0.1 }}
                          whileHover={{ scale: 1.05 }}
                        >
                          <span className="font-semibold text-melodyze-cyan">{formatKeyLabel(key)}:</span> {String(value)}
                        </motion.div>
                      ))}
                  </motion.div>
                </motion.div>

                {/* Content area (scrollable, 80%) */}
                <motion.div
                  className="overflow-y-auto border-t border-b border-melodyze-cyan/20 my-4 rounded-xl bg-melodyze-tertiary/20 backdrop-blur-sm"
                  style={{ flexBasis: "80%", minHeight: 0 }}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5, delay: 0.5 }}
                >
                  {/* If loading & no items => skeleton */}
                  {loading && items.length === 0 ? (
                    renderSkeleton()
                  ) : (
                    <>
                      {items.map((item) => (
                        <div
                          key={item._id}
                          className={`
                        flex items-center px-4 sm:px-6 py-4 gap-4 sm:gap-6
                        border-b border-melodyze-cyan/10
                        hover:bg-melodyze-secondary/40 hover:border-melodyze-cyan/30
                        transition-colors cursor-pointer rounded-lg mx-2 mb-2
                        ${item && item._id === selectedItem?._id
                              ? "border-melodyze-cyan/50 bg-melodyze-secondary/60 shadow-lg shadow-melodyze-cyan/10"
                              : "bg-melodyze-secondary/20"
                            }
                      `}
                          onClick={() => setSeletedItem(item)}
                        >
                          {/* Selection Indicator */}
                          <div className="flex-shrink-0">
                            {item && item._id === selectedItem?._id ? (
                              <CheckCircle2 className="text-melodyze-cyan w-6 h-6" />
                            ) : (
                              <div className="w-6 h-6 border-2 border-melodyze-cyan/40 rounded-full" />
                            )}
                          </div>

                          {/* Content Area (Properties and Audio Players) */}
                          <div className="flex flex-col w-full gap-2">
                            {/* Row 1: Properties */}
                            <div className="font-medium text-gray-200 flex flex-wrap gap-2 sm:gap-3 w-full">
                              {[
                                { label: "Source", value: item.source, color: "melodyze-cyan" },
                                { label: "Section", value: item.section, color: "melodyze-purple" },
                                { label: "Genre", value: item.genre, color: "melodyze-pink" },
                                { label: "Tempo", value: item.tempo, color: "melodyze-indigo" },
                                { label: "Pitch", value: `${item.pitch.replace("_sharp", "#")}`, color: "melodyze-cyan" },
                                { label: "Scale", value: item.scale, color: "melodyze-purple" },
                                { label: "Duration", value: `${item.duration_in_bars} bars`, color: "melodyze-pink" },
                              ].map((prop, idx) => {
                                const totalWidthUnits = Object.values(widthMap).reduce((sum, width) => sum + Number(width), 0);
                                const flexBasisPercent = (Number(widthMap[idx]) / totalWidthUnits) * 80;

                                return (
                                  <div
                                    key={idx}
                                    className={`relative flex items-center justify-center h-10 sm:h-12 bg-melodyze-tertiary/40 rounded-xl overflow-hidden border border-${prop.color}/30 flex-grow flex-shrink backdrop-blur-sm`}
                                    style={{
                                      flexBasis: `${flexBasisPercent}%`,
                                      minWidth: "0",
                                    }}
                                  >
                                    <div className="w-full h-full flex flex-col items-center justify-center p-2">
                                      <span className={`text-xs text-${prop.color}/80 font-medium`}>{prop.label}</span>
                                      <span
                                        className="block text-sm sm:text-base text-center font-semibold text-gray-200"
                                        style={{
                                          display: "inline-block",
                                          whiteSpace: "nowrap",
                                          overflow: "hidden",
                                          textOverflow: "ellipsis",
                                          maxWidth: "100%",
                                        }}
                                      >
                                        {prop.value}
                                      </span>
                                    </div>
                                  </div>
                                );
                              })}
                            </div>
                            {/* Row 2: Audio Players */}
                            <div className="flex flex-col sm:flex-row gap-4 w-full mt-1 justify-center">
                              <div className="flex items-center gap-2 w-full sm:w-1/2">
                                <AudioPlayer src={item.audio_path} label="Karaoke Audio" />
                              </div>
                              <div className="flex items-center gap-2 w-full sm:w-1/2">
                                <AudioPlayer src={item.audio_with_vocals_path} label="Audio With Vocals" />
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                      {/* If loading but already have items => partial skeleton below */}
                      {loading && items.length > 0 && renderSkeleton()}
                    </>
                  )}
                </motion.div>

                {/* Bottom Area: Load More & Choose Style (5%) */}
                <motion.div
                  className="flex flex-col justify-end flex-shrink-0 gap-4 pt-4"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.7 }}
                >
                  {hasMore && !loading && (
                    <motion.button
                      onClick={handleLoadMore}
                      className="
                      w-full text-center
                      bg-melodyze-tertiary/50 hover:bg-melodyze-tertiary/70
                      border border-melodyze-cyan/30 hover:border-melodyze-cyan/50
                      rounded-xl backdrop-blur-sm
                      py-3 text-sm font-medium text-gray-200
                      transition-all duration-300
                    "
                      whileHover={{ scale: 1.02, y: -1 }}
                      whileTap={{ scale: 0.98 }}
                      transition={{ type: "spring", stiffness: 300 }}
                    >
                      Load More
                    </motion.button>
                  )}

                  <div className="flex justify-end">
                    <AnimatePresence mode="wait">
                      {selectedItem ? (
                        <motion.button
                          key="choose-style"
                          onClick={handleSelectClick}
                          className="
                          bg-gradient-to-r from-melodyze-pink to-melodyze-purple
                          hover:from-melodyze-pink/80 hover:to-melodyze-purple/80
                          text-white px-8 py-3
                          rounded-xl font-semibold text-base
                          transition-all duration-300
                          shadow-lg hover:shadow-melodyze-pink/25
                        "
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          exit={{ opacity: 0, scale: 0.8 }}
                          whileHover={{ scale: 1.05, y: -2 }}
                          whileTap={{ scale: 0.95 }}
                          transition={{ type: "spring", stiffness: 300 }}
                        >
                          Choose Style
                        </motion.button>
                      ) : (
                        <motion.div
                          key="placeholder"
                          className="py-6"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          exit={{ opacity: 0 }}
                        />
                      )}
                    </AnimatePresence>
                  </div>
                </motion.div>
              </motion.div>
            </motion.div>,
            document.body
          )}
      </AnimatePresence>
    </div>
  );
};

export default SelectAudioItemDialog;
