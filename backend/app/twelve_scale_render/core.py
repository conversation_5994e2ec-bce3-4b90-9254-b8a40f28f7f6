import time
import json
import os
import shutil
import mido
import copy
from collections import Counter
from app.constants.constants import scale_list
from app.constants.gsheets import Gsheet
from app.utilities.midi_utils import MidiUtils
from app.utilities.commons import Commons

ticks_per_beat = 960
save_12_session = False
non_transposable_keywords = Gsheet.get_non_transposable_keywords()
vst_range_df = Gsheet.get_vst_range_list()
note_name_no = Gsheet.get_note_name_no()

def get_ticks_from_time(project, time):
    return int(project.time_to_beats(time) * ticks_per_beat)


# def transpose_tracks_reaper(project, shift):
#     # Iterate through tracks to find MIDI tracks
#     action_count = 0
#     for i, track in enumerate(project.tracks):
#         # Get all media items on the track
#         media_items = track.items
#
#         # Check if the track has any MIDI items
#         has_midi = False
#         for item in media_items:
#             # Get active take (this can be audio or MIDI)
#             take = item.active_take
#             if take.is_midi:
#                 has_midi = True
#                 # Get the MIDI data
#                 midi_data = take.notes
#                 # midi_data_dict['track_'+str(i)] = {j:n.infos for j,n  in enumerate(midi_data)}
#                 # print(f"Track {i+1} ('{track.name}') is a MIDI track.")
#                 # print(f"MIDI Data for Track {i+1}: {midi_data}")
#                 for j, note in enumerate(midi_data):
#                     note_start_tick = get_ticks_from_time(project, note.infos['start'])
#                     note_end_tick = get_ticks_from_time(project, note.infos['end'])
#                     args = (take.id, j, note.infos['selected'], note.infos['muted'], note_start_tick, note_end_tick,
#                             note.infos['channel'], note.infos['pitch'] + shift, note.infos['velocity'], False)
#                     RPR.MIDI_SetNote(*args)
#                     action_count += 1
#
#         # if not has_midi:
#         #     print(f"Track {i+1} ('{track.name}') is not a MIDI track.")
#     return action_count


# Check present high and low note in the track
def get_range_for_note(note: int, range_list: list):
    for index, range_ in enumerate(range_list):
        if (note in range(range_[0], range_[1] + 1)):
            return index
    return -1


def delete_temp_files(folder_path):
    """
    Deletes all files in the specified folder whose names start with 'temp'.

    :param folder_path: Path to the folder containing the files to be deleted.
    """
    try:
        # Check if the folder exists
        if not os.path.exists(folder_path):
            print(f"The folder '{folder_path}' does not exist.")
            return

        # Iterate over the files in the folder
        for filename in os.listdir(folder_path):
            # Construct the full file path
            file_path = os.path.join(folder_path, filename)

            # Check if it's a file and the name starts with 'temp'
            if os.path.isfile(file_path) and filename.startswith("temp"):
                # Delete the file
                os.remove(file_path)
                # print(f"Deleted: {file_path}")

    except Exception as e:
        print(f"An error occurred: {e}")


# A midi track can have multiple channel
# We need to enforce that the midi ttrack can have only one channel
# detect_majority_channel identifies the channel no which has majority no of midi messages
# Later we assign the majority channel to all messages in the tracks

def detect_majority_channel(track):
    channel_counts = Counter()

    # Count the occurrences of each channel in the file
    has_channel = False
    for message in track:
        if message.type in ['note_on', 'note_off', 'control_change', 'program_change']:
            channel_counts[message.channel] += 1
            has_channel = True

    # Detect the majority channel
    majority_channel = channel_counts.most_common(1)[0][0]
    return majority_channel


def get_modified_shift(vst_range_df, midi, project, d_shift, u_shift):
    import reapy.reascript_api as RPR

    midi_file = mido.MidiFile(midi)

    # note_name_no = get_note_name_no()
    global_shift_map = {}
    for i in range(d_shift, u_shift + 1):
        global_shift_map[i] = i

    modification_flag = False

    print("\n***Looking for whole arrangement shift...")
    # Loop to globally change the shift
    rpr_track_name_list = []
    for i, rpr_track in enumerate(project.tracks):
        rpr_track_name = rpr_track.name
        # if track name same then make it unique
        while (rpr_track_name in rpr_track_name_list):
            rpr_track_name = rpr_track_name + "_"
        rpr_track_name_list.append(rpr_track_name)

        mido_track_name_list = []
        for j, mido_track in enumerate(midi_file.tracks):
            mido_track_name: str = mido_track.name
            # if track name same then make it unique
            while (mido_track_name in mido_track_name_list):
                mido_track_name = mido_track_name + "_"
            mido_track_name_list.append(mido_track_name)

            if not rpr_track_name == mido_track_name:
                continue

            this_allowed_range = []

            transposable = True
            for keyword in non_transposable_keywords:
                if Commons.search_word_in_string(keyword, mido_track_name.lower()):
                    transposable = False
                    break
            if not transposable:
                break

            num_fx = RPR.TrackFX_GetCount(rpr_track.id)
            for index, row in enumerate(vst_range_df):  #.iterrows():
                if index == 0:
                    continue
                vst_name_range = str(row[0])
                low = int(note_name_no[str(row[1]).upper()])
                high = int(note_name_no[str(row[2]).upper()])

                if Commons.search_word_in_string(vst_name_range.lower(), rpr_track_name.lower()):
                    this_allowed_range.append((low, high))
                    continue

                for fx_index in range(num_fx):
                    vst_name_current_track: str = RPR.TrackFX_GetFXName(rpr_track.id, fx_index, "", 1024)[3]
                    if Commons.search_word_in_string(vst_name_range.lower(), vst_name_current_track.lower()):
                        this_allowed_range.append((low, high))

            if len(this_allowed_range) == 0:
                this_allowed_range.append((0, 999))



            if transposable:
                for msg in mido_track:
                    if msg.type == 'note_on' or msg.type == 'note_off':
                        this_allowed_range_index = get_range_for_note(msg.note, this_allowed_range)
                        if this_allowed_range_index < 0:
                            continue
                        this_allowed_range_min = this_allowed_range[this_allowed_range_index][0]
                        this_allowed_range_max = this_allowed_range[this_allowed_range_index][1]

                        if msg.note + u_shift > this_allowed_range_max:
                            if modification_flag == False:
                                print(f"\n!!!!Whole Arrangement shift Triggered for track: {mido_track_name} !!!!")
                                print(f"Notes Crossing Highest Boundary for the following shifts::")
                            for i in range(1, u_shift + 1):
                                if msg.note + global_shift_map[i] > this_allowed_range_max:
                                    print(f"{i},", end=" ")
                                    global_shift_map[i] = i - 12
                            modification_flag = True
                        if msg.note + d_shift < this_allowed_range_min:
                            if modification_flag == False:
                                print(f"\n!!!!Whole Arrangement shift Triggered for track: {mido_track_name} !!!!")
                                print(f"Notes Crossing Lowest Boundary for the following shifts::")
                            for i in range(d_shift, 0):
                                if msg.note + global_shift_map[i] < this_allowed_range_min:
                                    print(f"{i},", end=" ")
                                    global_shift_map[i] = i + 12
                            modification_flag = True

        if modification_flag:
            break

    if not modification_flag:
        print(f"\nBravo.... All the notes are transposable within {d_shift} and {u_shift}. No global shift required.")

    d_shift = min(global_shift_map.values())
    u_shift = max(global_shift_map.values())

    if modification_flag:
        print("\n***Looking for individual track level shift further (if required)...")

    track_level_shift = {}
    # Loop to globally change the shift
    rpr_track_name_list = []
    for i, rpr_track in enumerate(project.tracks):
        rpr_track_name = rpr_track.name
        # if track name same then make it unique
        while (rpr_track_name in rpr_track_name_list):
            rpr_track_name = rpr_track_name + "_"
        rpr_track_name_list.append(rpr_track_name)

        mido_track_name_list = []
        for j, mido_track in enumerate(midi_file.tracks):
            mido_track_name: str = mido_track.name
            # if track name same then make it unique
            while (mido_track_name in mido_track_name_list):
                mido_track_name = mido_track_name + "_"
            mido_track_name_list.append(mido_track_name)

            if not rpr_track_name == mido_track_name:
                continue

            track_level_shift[mido_track_name] = {}

            this_allowed_range = []

            transposable = True
            for keyword in non_transposable_keywords:
                if Commons.search_word_in_string(keyword, mido_track_name.lower()):
                    transposable = False
                    break
            if not transposable:
                break

            num_fx = RPR.TrackFX_GetCount(rpr_track.id)

            for index, row in enumerate(vst_range_df): #.iterrows():
                if index == 0:
                    continue
                vst_name_range = str(row[0])
                low = int(note_name_no[str(row[1]).upper()])
                high = int(note_name_no[str(row[2]).upper()])

                if Commons.search_word_in_string(vst_name_range.lower(), rpr_track_name.lower()):
                    this_allowed_range.append((low, high))
                    continue

                for fx_index in range(num_fx):
                    vst_name_current_track: str = RPR.TrackFX_GetFXName(rpr_track.id, fx_index, "", 1024)[3]
                    if Commons.search_word_in_string(vst_name_range.lower(), vst_name_current_track.lower()):
                        this_allowed_range.append((low, high))
            if len(this_allowed_range) == 0:
                this_allowed_range.append((0, 999))

            for range_ in this_allowed_range:
                track_level_shift[mido_track_name][range_] = {}
                for i in range(d_shift, u_shift + 1):
                    track_level_shift[mido_track_name][range_][i] = i

            # range_used = []
            modified_flag = False
            for msg in mido_track:
                if msg.type == 'note_on' or msg.type == 'note_off':
                    this_allowed_range_index = get_range_for_note(msg.note, this_allowed_range)
                    if this_allowed_range_index < 0:
                        continue
                    this_range = this_allowed_range[this_allowed_range_index]
                    # if this_range in range_used:
                    #     continue
                    this_allowed_range_min = this_range[0]
                    this_allowed_range_max = this_range[1]

                    if msg.note + u_shift > this_allowed_range_max:
                        if modified_flag == False:
                            print(f"\n\n\n!!!! Individual Track level shift Triggered for track: {mido_track_name} !!!!")
                            print(f"Notes Crossing Highest Boundary for the range: {this_range}")
                        for i in range(1, u_shift + 1):
                            new_i = track_level_shift[mido_track_name][this_range][i]
                            if msg.note + new_i > this_allowed_range_max:
                                track_level_shift[mido_track_name][this_range][i] -= 12
                                modified_flag = True
                        # range_used.append(this_range)
                    if msg.note + d_shift < this_allowed_range_min:
                        if modified_flag == False:
                            print(f"\n\n\n!!!! Individual Track level shift Triggered for track: {mido_track_name} !!!!")
                            print(f"Notes Crossing Lowest Boundary for the range: {this_range}")
                        for i in range(d_shift, 0):
                            new_i = track_level_shift[mido_track_name][this_range][i]
                            if msg.note + new_i < this_allowed_range_min:
                                track_level_shift[mido_track_name][this_range][i] += 12
                                modified_flag = True
                        # range_used.append(this_range)

    return global_shift_map, track_level_shift


def transpose_tracks_mido(midi, project, project_path, initial_midi_pos, output_folder_path, shift, track_level_shift):
    import reapy.reascript_api as RPR

    # Read MIDI fileD#
    midi_file = mido.MidiFile(midi)
    temp_midi_path = str(os.path.join(output_folder_path, "temp.mid"))

    # print(temp_midi_path)
    # note_name_no = get_note_name_no()

    # Loop to transpose each track and inport into reaper project
    rpr_track_name_list = []
    for i, rpr_track in enumerate(project.tracks):
        rpr_track_name = rpr_track.name
        # if track name same then make it unique
        while (rpr_track_name in rpr_track_name_list):
            rpr_track_name = rpr_track_name + "_"
        rpr_track_name_list.append(rpr_track_name)

        mido_track_name_list = []
        for j, mido_track in enumerate(midi_file.tracks):
            mido_track_name: str = mido_track.name
            # if track name same then make it unique
            while (mido_track_name in mido_track_name_list):
                mido_track_name = mido_track_name + "_"
            mido_track_name_list.append(mido_track_name)

            if not rpr_track_name == mido_track_name:
                continue

            majority_channel = detect_majority_channel(mido_track)

            # MIDO

            # new_track = copy.deepcopy(mido_track)


            new_midi = mido.MidiFile(ticks_per_beat=midi_file.ticks_per_beat)
            new_track = mido.MidiTrack()

            transposable = True
            for keyword in non_transposable_keywords:
                if Commons.search_word_in_string(keyword, mido_track_name.lower()):
                    transposable = False
                    break

            if not transposable:
                for message in mido_track:
                    if message.type in ['note_on', 'note_off', 'control_change', 'program_change']:
                        # Move the message to the majority channel
                        new_message = message.copy(channel=majority_channel)
                        new_track.append(new_message)
                    else:
                        # Append non-channel messages unchanged (e.g., meta messages)
                        new_track.append(message)
            else:
                if len(track_level_shift) > 0:
                    range_wise_shift = track_level_shift[mido_track_name]
                else:
                    range_wise_shift = None

                for msg in mido_track:
                    if msg.type in ['control_change', 'program_change']:
                        new_msg = msg.copy(channel=majority_channel)
                    elif msg.type in ['note_on', 'note_off']:
                        if (not range_wise_shift is None) and len(range_wise_shift) > 0:
                            this_allowed_range = list(range_wise_shift.keys())
                            this_allowed_range_index = get_range_for_note(msg.note, this_allowed_range)

                            # Note outside transposable range
                            if this_allowed_range_index < 0:
                                new_msg_ = copy.deepcopy(msg)
                            # Note within transposable range
                            else:
                                this_range = this_allowed_range[this_allowed_range_index]
                                shifts = range_wise_shift[this_range]

                                new_msg_ = mido.Message(msg.type, note=msg.note + shifts[shift],
                                                        velocity=msg.velocity, time=msg.time)
                        else:
                            new_msg_ = copy.deepcopy(msg)
                        new_msg = new_msg_.copy(channel=majority_channel)
                    else:
                        new_msg = copy.deepcopy(msg)
                    new_track.append(new_msg)

            new_midi.tracks.append(new_track)
            new_midi.save(temp_midi_path)

            # REAPER

            RPR.SetEditCurPos(initial_midi_pos, True, True)

            RPR.SetOnlyTrackSelected(rpr_track.id)
            midi_segments = len(rpr_track.items)
            for i in range(midi_segments):
                rpr_track.items[0].delete()
            RPR.InsertMedia(temp_midi_path, 0)
            RPR.SetEditCurPos(initial_midi_pos, True, True)
    delete_temp_files(os.path.join(os.path.split(project_path)[0], "Media"))
    delete_temp_files(output_folder_path)

def convert_tuple_keys(obj):
    """Recursively convert tuple keys in dictionaries to strings."""
    if isinstance(obj, dict):
        return {str(k) if isinstance(k, tuple) else k: convert_tuple_keys(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_tuple_keys(i) for i in obj]
    else:
        return obj

def export_twelve_scales(down_shift, up_shift, MIDI_path, project_path, output_folder_path, shift_to_scale_dict,
                         default_scale):
    import reapy
    import reapy.reascript_api as RPR

    # Get the current project (the one that is currently open in REAPER)
    project = reapy.Project()
    # Set project to ignore audio device sample rate
    RPR.GetSetProjectInfo(0, "PROJECT_SRATE_USE", 48000.0, True)

    RPR.GetSetProjectInfo(0, "PROJECT_SRATE", 48000.0, True)

    output_wav_path = os.path.join(output_folder_path, "Wavs")
    if not os.path.exists(output_wav_path):
        os.makedirs(output_wav_path)

    if save_12_session:
        output_proj_path = os.path.join(output_folder_path, "Sessions")
        if not os.path.exists(output_proj_path):
            os.makedirs(output_proj_path)

    initial_midi_pos = MidiUtils.get_initial_midi_pos()

    # vst_range_df = pd.read_csv('vst_range_list.csv', header=None)
    # vst_range_df = get_vst_range_list()

    _, _, start_time, end_time, _ = RPR.GetSet_LoopTimeRange(False, False, 0, 0, False)
    proj_end_time = RPR.GetProjectLength(0)

    start_pct = start_time / proj_end_time
    end_pct = end_time / proj_end_time

    if start_time == end_time:
        print("\n*** No range selected. Rendering the entire project ***\n")
        start_pct = 0.0
        end_pct = 1.0

    global_shift_map, track_level_shift = get_modified_shift(vst_range_df, MIDI_path, project, down_shift, up_shift)

    converted_data = convert_tuple_keys(track_level_shift)
    print(json.dumps(converted_data, indent=4))

    # Render default scale
    transpose_tracks_mido(MIDI_path, project, project_path, initial_midi_pos, output_folder_path, 0, track_level_shift)

    RPR.Main_SaveProject(project, False)

    fname = default_scale
    new_wav_path = os.path.join(output_wav_path, f'{fname}.wav')
    # RPR.SetEditCurPos(0, True, True)
    RPR.RenderFileSection(project_path, new_wav_path, start_pct, end_pct, 1)
    if save_12_session:
        new_proj_path = os.path.join(output_proj_path, f'{fname}.rpp')
        RPR.Main_SaveProjectEx(None, new_proj_path, 0)

    # Render other scale
    for key in range(down_shift, up_shift + 1):
        if key == 0:
            continue
        if len(global_shift_map) > 0:
            transpose_tracks_mido(MIDI_path, project, project_path, initial_midi_pos, output_folder_path, 
                                  global_shift_map[key], track_level_shift)
        else:
            transpose_tracks_mido(MIDI_path, project, project_path, initial_midi_pos, output_folder_path,
                                  key, track_level_shift)
        RPR.Main_SaveProject(project, False)

        fname = f'{shift_to_scale_dict[key]}'
        new_wav_path = os.path.join(output_wav_path, f'{fname}.wav')
        # RPR.SetEditCurPos(0, True, True)
        RPR.RenderFileSection(project_path, new_wav_path, start_pct, end_pct, 1)
        if save_12_session:
            new_proj_path = os.path.join(output_proj_path, f'{fname}.rpp')
            RPR.Main_SaveProjectEx(None, new_proj_path, 0)

    # Set project MIDI to default scale
    transpose_tracks_mido(MIDI_path, project, project_path, initial_midi_pos, output_folder_path, 0,
                          track_level_shift)
    RPR.Main_SaveProject(project, False)

    # Delete temp MIDI file
    if os.path.exists(os.path.join(output_folder_path, "temp.mid")):
        os.remove(os.path.join(output_folder_path, "temp.mid"))


def router(data):
    import reapy
    import reapy.reascript_api as RPR

    # print(data)
    # return
    print("#####Router called#####")
    global save_12_session
    save_12_session = data['save_12_session']

    down_shift = data['down_shift']
    if down_shift > 0:
        down_shift *= -1

    up_shift = data['up_shift']

    MIDI_path = data['MIDI_path']
    project_path = data['project_path']

    project_dir, project_file = os.path.split(project_path)
    project_file_wo_ext, _ = os.path.splitext(project_file)
    output_folder_path = os.path.join(data["output_dir"], project_file_wo_ext + " - 12 exports")
    if os.path.exists(output_folder_path):
        shutil.rmtree(output_folder_path)
        os.makedirs(output_folder_path)

    default_scale = data['default_scale']

    if not len(range(down_shift, up_shift + 1)) == 12:
        print("Range is not covering 12 keys...\nModify \"down_shift\" and \"up_shift\" properly in config.json")
        return

    try:
        default_scale_index = scale_list.index(default_scale)
    except ValueError:
        print(f"'{default_scale}' is not a valid scale.\n"
              f"Provide input from following scales inside config.json: \n'"
              f"{scale_list}'")
        return

    shift_to_scale_dict = {}
    for i in range(down_shift, up_shift + 1):
        shift_to_scale_dict[i] = scale_list[(default_scale_index + i) % 12]

    RPR.Main_openProject(project_path)
    total_wait_time = data['total_wait_time']
    for remaining_time in range(total_wait_time, 0, -1):
        print(f"\rWaiting for VST loading... Time remaining: {remaining_time} seconds", end="")
        time.sleep(1)

    # After waiting is done
    print("\rWaiting for VST loading... Time remaining: 0 seconds. Done!")

    export_twelve_scales(down_shift, up_shift, MIDI_path, project_path, output_folder_path, shift_to_scale_dict,
                         default_scale)
    RPR.Main_OnCommand(40860, 0)
